package com.dinglite.business.control.controller.product;

import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.common.global.GlobalResult;
import com.dinglite.product.api.domain.dto.ProductOrderLossDetailDTO;
import com.dinglite.product.api.domain.query.ProductOrderLossDetailQuery;
import com.dinglite.product.api.service.ProductOrderLossDetailClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 生产工单损耗明细管理
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/product/product-order-loss-detail")
public class ProductOrderLossDetailController {

    private final ProductOrderLossDetailClient productOrderLossDetailClient;

    /**
     * 获取损耗明细详情
     *
     * @param productOrderLossDetailId 损耗明细ID
     * @return 损耗明细DTO
     */
    @GetMapping
    public GlobalResult<ProductOrderLossDetailDTO> getInfo(@RequestParam Long productOrderLossDetailId) {
        return GlobalResult.success(productOrderLossDetailClient.getInfo(productOrderLossDetailId));
    }

    /**
     * 分页查询损耗明细
     *
     * @param productOrderLossDetailQuery 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @LoadPayload
    public GlobalResult<PageDTO<ProductOrderLossDetailDTO>> page(@RequestBody ProductOrderLossDetailQuery productOrderLossDetailQuery) {
        return GlobalResult.success(productOrderLossDetailClient.page(productOrderLossDetailQuery));
    }

    /**
     * 查询损耗明细列表
     *
     * @param productOrderLossDetailQuery 查询条件
     * @return 损耗明细列表
     */
    @PostMapping("/list")
    @LoadPayload
    public GlobalResult<List<ProductOrderLossDetailDTO>> list(@RequestBody ProductOrderLossDetailQuery productOrderLossDetailQuery) {
        return GlobalResult.success(productOrderLossDetailClient.list(productOrderLossDetailQuery));
    }

    /**
     * 根据损耗单ID查询明细列表
     *
     * @param productOrderLossId 损耗单ID
     * @return 明细列表
     */
    @GetMapping("/by-loss-id")
    public GlobalResult<List<ProductOrderLossDetailDTO>> listByLossId(@RequestParam Long productOrderLossId) {
        return GlobalResult.success(productOrderLossDetailClient.listByLossId(productOrderLossId));
    }

    /**
     * 根据物料ID查询损耗明细列表
     *
     * @param materialId 物料ID
     * @return 明细列表
     */
    @GetMapping("/by-material-id")
    public GlobalResult<List<ProductOrderLossDetailDTO>> listByMaterialId(@RequestParam Long materialId) {
        return GlobalResult.success(productOrderLossDetailClient.listByMaterialId(materialId));
    }

    /**
     * 根据生产工单ID查询损耗明细列表
     *
     * @param productOrderId 生产工单ID
     * @return 明细列表
     */
    @GetMapping("/by-product-order-id")
    public GlobalResult<List<ProductOrderLossDetailDTO>> listByProductOrderId(@RequestParam Long productOrderId) {
        return GlobalResult.success(productOrderLossDetailClient.listByProductOrderId(productOrderId));
    }

    /**
     * 统计指定物料的损耗总数量
     *
     * @param materialId 物料ID
     * @return 损耗总数量
     */
    @GetMapping("/sum-loss-num")
    public GlobalResult<BigDecimal> sumLossNumByMaterialId(@RequestParam Long materialId) {
        return GlobalResult.success(productOrderLossDetailClient.sumLossNumByMaterialId(materialId));
    }

    /**
     * 查询指定时间范围内的损耗明细
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 明细列表
     */
    @GetMapping("/time-range")
    public GlobalResult<List<ProductOrderLossDetailDTO>> listByTimeRange(@RequestParam String startTime,
                                                                         @RequestParam String endTime) {
        return GlobalResult.success(productOrderLossDetailClient.listByTimeRange(startTime, endTime));
    }
}
