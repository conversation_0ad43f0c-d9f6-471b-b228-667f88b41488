package com.dinglite.business.control.controller.product;

import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.annotation.Log;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.common.global.GlobalResult;
import com.dinglite.product.api.domain.dto.ProductOrderLossDTO;
import com.dinglite.product.api.domain.query.ProductOrderLossQuery;
import com.dinglite.product.api.service.ProductOrderLossClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 生产工单损耗单管理
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@RestController
@RequiredArgsConstructor
@Slf4j
@RequestMapping("/product/product-order-loss")
public class ProductOrderLossController {

    private final ProductOrderLossClient productOrderLossClient;

    /**
     * 创建损耗单
     *
     * @param productOrderLossDTO 损耗单DTO
     * @return 损耗单DTO
     */
    @PostMapping
    @LoadPayload
    @Log(title = "生产工单损耗单管理-创建")
    public GlobalResult<ProductOrderLossDTO> create(@RequestBody @Valid ProductOrderLossDTO productOrderLossDTO) {
        return GlobalResult.success(productOrderLossClient.create(productOrderLossDTO));
    }

    /**
     * 获取损耗单详情
     *
     * @param productOrderLossId 损耗单ID
     * @return 损耗单DTO
     */
    @GetMapping
    public GlobalResult<ProductOrderLossDTO> getInfo(@RequestParam Long productOrderLossId) {
        return GlobalResult.success(productOrderLossClient.getInfo(productOrderLossId));
    }

    /**
     * 更新损耗单
     *
     * @param productOrderLossDTO 损耗单DTO
     * @return 是否成功
     */
    @PutMapping
    @LoadPayload
    @Log(title = "生产工单损耗单管理-更新")
    public GlobalResult<Boolean> update(@RequestBody @Valid ProductOrderLossDTO productOrderLossDTO) {
        return GlobalResult.status(productOrderLossClient.update(productOrderLossDTO));
    }

    /**
     * 删除损耗单
     *
     * @param productOrderLossIds 损耗单ID列表
     * @return 是否成功
     */
    @DeleteMapping
    @LoadPayload
    @Log(title = "生产工单损耗单管理-删除")
    public GlobalResult<Boolean> delete(@RequestBody List<Long> productOrderLossIds) {
        return GlobalResult.status(productOrderLossClient.delete(productOrderLossIds));
    }

    /**
     * 分页查询损耗单
     *
     * @param productOrderLossQuery 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    @LoadPayload
    public GlobalResult<PageDTO<ProductOrderLossDTO>> page(@RequestBody ProductOrderLossQuery productOrderLossQuery) {
        return GlobalResult.success(productOrderLossClient.page(productOrderLossQuery));
    }

    /**
     * 查询损耗单列表
     *
     * @param productOrderLossQuery 查询条件
     * @return 损耗单列表
     */
    @PostMapping("/list")
    @LoadPayload
    public GlobalResult<List<ProductOrderLossDTO>> list(@RequestBody ProductOrderLossQuery productOrderLossQuery) {
        return GlobalResult.success(productOrderLossClient.list(productOrderLossQuery));
    }

    /**
     * 审核损耗单
     *
     * @param productOrderLossId 损耗单ID
     * @param status             审核状态
     * @param checkRemark        审核备注
     * @return 是否成功
     */
    @PostMapping("/approve")
    @LoadPayload
    @Log(title = "生产工单损耗单管理-审核")
    public GlobalResult<Boolean> approve(@RequestParam Long productOrderLossId,
                                        @RequestParam Integer status,
                                        @RequestParam(required = false) String checkRemark) {
        return GlobalResult.status(productOrderLossClient.approve(productOrderLossId, status, checkRemark));
    }

    /**
     * 批量审核损耗单
     *
     * @param productOrderLossIds 损耗单ID列表
     * @param status              审核状态
     * @param checkRemark         审核备注
     * @return 是否成功
     */
    @PostMapping("/batch-approve")
    @LoadPayload
    @Log(title = "生产工单损耗单管理-批量审核")
    public GlobalResult<Boolean> batchApprove(@RequestParam List<Long> productOrderLossIds,
                                             @RequestParam Integer status,
                                             @RequestParam(required = false) String checkRemark) {
        return GlobalResult.status(productOrderLossClient.batchApprove(productOrderLossIds, status, checkRemark));
    }

    /**
     * 根据生产工单ID查询损耗单列表
     *
     * @param productOrderId 生产工单ID
     * @return 损耗单列表
     */
    @GetMapping("/by-product-order")
    public GlobalResult<List<ProductOrderLossDTO>> listByProductOrderId(@RequestParam Long productOrderId) {
        return GlobalResult.success(productOrderLossClient.listByProductOrderId(productOrderId));
    }

    /**
     * 根据产线ID查询损耗单列表
     *
     * @param productLineId 产线ID
     * @return 损耗单列表
     */
    @GetMapping("/by-product-line")
    public GlobalResult<List<ProductOrderLossDTO>> listByProductLineId(@RequestParam Long productLineId) {
        return GlobalResult.success(productOrderLossClient.listByProductLineId(productLineId));
    }

    /**
     * 根据审核状态查询损耗单列表
     *
     * @param status 审核状态
     * @return 损耗单列表
     */
    @GetMapping("/by-status")
    public GlobalResult<List<ProductOrderLossDTO>> listByStatus(@RequestParam Integer status) {
        return GlobalResult.success(productOrderLossClient.listByStatus(status));
    }
}
