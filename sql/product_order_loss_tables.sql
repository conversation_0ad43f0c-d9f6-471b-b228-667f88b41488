-- =============================================
-- 生产工单损耗管理模块数据库表结构设计
-- =============================================

-- 1. 生产工单损耗单主表
DROP TABLE IF EXISTS `product_order_loss`;
CREATE TABLE `product_order_loss` (
  `product_order_loss_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '生产工单损耗单ID',
  `product_order_loss_code` varchar(50) NOT NULL COMMENT '生产工单损耗单流水号',
  `product_line_id` bigint(20) NOT NULL COMMENT '产线ID',
  `product_line_name` varchar(100) NOT NULL COMMENT '产线名称',
  `product_order_id` bigint(20) NOT NULL COMMENT '生产工单ID',
  `product_order_code` varchar(50) NOT NULL COMMENT '生产工单编码',
  `product_order_name` varchar(200) NOT NULL COMMENT '生产工单名称',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '审核状态（0.待审核 1.审核通过 2.审核未通过）',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `check_by` varchar(50) DEFAULT NULL COMMENT '审核人',
  `check_time` datetime DEFAULT NULL COMMENT '审核时间',
  `check_remark` text COMMENT '审核备注',
  `flag` char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除（Y是 N否）',
  PRIMARY KEY (`product_order_loss_id`),
  UNIQUE KEY `uk_product_order_loss_code` (`product_order_loss_code`),
  KEY `idx_product_line_id` (`product_line_id`),
  KEY `idx_product_order_id` (`product_order_id`),
  KEY `idx_status` (`status`),
  KEY `idx_create_time` (`create_time`),
  KEY `idx_create_by` (`create_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产工单损耗单主表';

-- 2. 生产工单损耗明细表
DROP TABLE IF EXISTS `product_order_loss_detail`;
CREATE TABLE `product_order_loss_detail` (
  `product_order_loss_detail_id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '生产工单损耗明细ID',
  `product_order_loss_id` bigint(20) NOT NULL COMMENT '生产工单损耗单ID',
  `material_id` bigint(20) NOT NULL COMMENT '物料ID',
  `material_code` varchar(50) NOT NULL COMMENT '物料编码',
  `material_name` varchar(200) NOT NULL COMMENT '物料名称',
  `material_spec` varchar(200) DEFAULT NULL COMMENT '物料规格',
  `unit` varchar(20) DEFAULT NULL COMMENT '单位',
  `loss_num` decimal(15,4) NOT NULL DEFAULT '0.0000' COMMENT '损耗数量',
  `remark` text COMMENT '备注',
  `create_by` varchar(50) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` varchar(50) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `flag` char(1) NOT NULL DEFAULT 'N' COMMENT '逻辑删除（Y是 N否）',
  PRIMARY KEY (`product_order_loss_detail_id`),
  KEY `idx_product_order_loss_id` (`product_order_loss_id`),
  KEY `idx_material_id` (`material_id`),
  KEY `idx_material_code` (`material_code`),
  KEY `idx_create_time` (`create_time`),
  CONSTRAINT `fk_product_order_loss_detail_main` FOREIGN KEY (`product_order_loss_id`) REFERENCES `product_order_loss` (`product_order_loss_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='生产工单损耗明细表';

-- 3. 编码规则配置（必须配置）
-- 重要：编码规则配置是必须的！请执行 product_order_loss_encode_rule_config.sql 文件
-- 如果不配置编码规则，系统在创建损耗单时会失败

-- 示例数据插入（可选，用于测试）
-- INSERT INTO `product_order_loss` (
--   `product_order_loss_code`, `product_line_id`, `product_line_name`,
--   `product_order_id`, `product_order_code`, `product_order_name`,
--   `status`, `create_by`
-- ) VALUES (
--   'LOSS202507290001', 2, '2线',
--   13, 'PO25072548000005', '221',
--   0, 'admin'
-- );

-- INSERT INTO `product_order_loss_detail` (
--   `product_order_loss_id`, `material_id`, `material_code`, `material_name`,
--   `loss_num`, `create_by`
-- ) VALUES (
--   1, 11, 'YEA0068-075V2', '反射(卷材)',
--   1000.0000, 'admin'
-- );
