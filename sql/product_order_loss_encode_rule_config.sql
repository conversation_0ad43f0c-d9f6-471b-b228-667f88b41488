-- =============================================
-- 生产工单损耗管理模块 - 编码规则配置（必须配置）
-- =============================================

-- 注意：编码规则配置是必须的！
-- 如果不配置编码规则，系统在创建损耗单时会调用 EncodeRuleClient.getSerialNumber() 方法失败
-- 导致损耗单无法正常创建
-- 也可通过系统创建

-- 1. 插入编码规则主表记录
INSERT INTO `encode_rule` (
    `name`, 
    `table_name`, 
    `field_name`, 
    `zero_type`, 
    `zero_value`, 
    `max_value`, 
    `create_by`, 
    `create_time`
) VALUES (
    '生产工单损耗单编码规则',                    -- 编码规则名称
    'product_order_loss',                      -- 表名
    'product_order_loss_code',                 -- 字段名
    1,                                         -- 归零类型：1.按日归零
    1,                                         -- 归零值：从1开始
    '999999',                                  -- 最大值：6位数字
    'system',                                  -- 创建人
    NOW()                                      -- 创建时间
);

-- 获取刚插入的编码规则ID（用于明细表）
SET @encode_rule_id = LAST_INSERT_ID();

-- 2. 插入编码规则明细表记录（定义编码格式）
-- 编码格式：LOSS + YYYYMMDD + 6位流水号
-- 示例：LOSS20250729000001

-- 2.1 固定前缀 "LOSS"
INSERT INTO `encode_rule_detail` (
    `encode_rule_id`,
    `type`,
    `content`,
    `content_length`,
    `sort_order`,
    `create_by`,
    `create_time`
) VALUES (
    @encode_rule_id,                           -- 编码规则ID
    0,                                         -- 类型：0.固定值
    'LOSS',                                    -- 内容：固定前缀
    4,                                         -- 长度：4位
    1,                                         -- 排序：第1位
    'system',                                  -- 创建人
    NOW()                                      -- 创建时间
);

-- 2.2 日期格式 "YYYYMMDD"
INSERT INTO `encode_rule_detail` (
    `encode_rule_id`,
    `type`,
    `content`,
    `content_length`,
    `sort_order`,
    `create_by`,
    `create_time`
) VALUES (
    @encode_rule_id,                           -- 编码规则ID
    1,                                         -- 类型：1.时间日期格式
    'yyyyMMdd',                                -- 内容：日期格式
    8,                                         -- 长度：8位
    2,                                         -- 排序：第2位
    'system',                                  -- 创建人
    NOW()                                      -- 创建时间
);

-- 2.3 流水号 "000001"
INSERT INTO `encode_rule_detail` (
    `encode_rule_id`,
    `type`,
    `content`,
    `content_length`,
    `sort_order`,
    `create_by`,
    `create_time`
) VALUES (
    @encode_rule_id,                           -- 编码规则ID
    3,                                         -- 类型：3.流水号
    '',                                        -- 内容：流水号无需内容
    6,                                         -- 长度：6位
    3,                                         -- 排序：第3位
    'system',                                  -- 创建人
    NOW()                                      -- 创建时间
);

-- 3. 验证配置结果
SELECT 
    er.encode_rule_id,
    er.name,
    er.table_name,
    er.field_name,
    er.zero_type,
    er.zero_value,
    er.max_value
FROM encode_rule er 
WHERE er.table_name = 'product_order_loss' 
  AND er.field_name = 'product_order_loss_code';

SELECT 
    erd.encode_rule_detail_id,
    erd.encode_rule_id,
    erd.type,
    erd.content,
    erd.content_length,
    erd.sort_order,
    CASE erd.type 
        WHEN 0 THEN '固定值'
        WHEN 1 THEN '时间日期格式'
        WHEN 2 THEN '随机数'
        WHEN 3 THEN '流水号'
        ELSE '未知类型'
    END as type_desc
FROM encode_rule_detail erd 
WHERE erd.encode_rule_id = @encode_rule_id
ORDER BY erd.sort_order;

-- 4. 测试编码规则（可选）
-- 注意：实际测试需要通过 EncodeRuleClient.getSerialNumber() 方法调用
-- 这里只是展示预期的编码格式

SELECT CONCAT(
    'LOSS',                                    -- 固定前缀
    DATE_FORMAT(NOW(), '%Y%m%d'),             -- 当前日期
    LPAD('1', 6, '0')                         -- 6位流水号（从000001开始）
) as sample_code;

-- 预期结果示例：LOSS20250729000001

-- 5. 重要说明
-- =====================================
-- 编码规则配置说明：
-- 
-- 1. 必须性：编码规则配置是必须的，不配置会导致系统无法正常创建损耗单
-- 
-- 2. 唯一性：表名+字段名的组合必须唯一，不能重复配置
-- 
-- 3. 归零类型说明：
--    0 = 不归零（流水号持续递增）
--    1 = 按日归零（每天重新从归零值开始）
--    2 = 按月归零（每月重新从归零值开始）
--    3 = 按年归零（每年重新从归零值开始）
-- 
-- 4. 明细类型说明：
--    0 = 固定值（如前缀 "LOSS"）
--    1 = 时间日期格式（如 "yyyyMMdd"）
--    2 = 随机数
--    3 = 流水号（自动递增的数字）
-- 
-- 5. 如果需要修改编码格式，请修改 encode_rule_detail 表中的配置
-- 
-- 6. 删除编码规则时，请先删除 encode_rule_detail 表中的明细记录，
--    再删除 encode_rule 表中的主记录
-- =====================================
