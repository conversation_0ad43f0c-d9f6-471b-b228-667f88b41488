package com.mes.rocketmq;

import com.alibaba.fastjson.JSON;
import com.dinglite.common.rocket.MqTopic;
import com.dinglite.common.utils.CommonUuidUtils;
import jodd.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.rocketmq.client.producer.*;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.common.message.MessageQueue;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.SmartLifecycle;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

import java.io.*;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.*;

/**
 * rocketMq配置
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Slf4j
@Configuration
public class RocketMqConfiguration implements SmartLifecycle, InitializingBean {


    @Autowired
    MQProducer mqProducer;

//    @Autowired
//    TransactionMQProducer transactionMQProducer;

    @Autowired
    ResourceLoader resourceLoader;

    final LinkedBlockingQueue<CustomerMessage> linedBlock;

    // 记录递归次数
    final ConcurrentHashMap<String, RecursionMessage> concurrentHashMapObject;

    // 定时任务线程池
    final ScheduledThreadPoolExecutor threadPoolExecutor;

    {
        // 必须随 WEB容器关闭而关闭
        ThreadFactory threadFactory = ThreadFactoryBuilder.create().setDaemon(true).setNameFormat("poole-mq-Scheduled-").get();
        threadPoolExecutor = new ScheduledThreadPoolExecutor(2, threadFactory);
        concurrentHashMapObject = new ConcurrentHashMap();
        linedBlock = new LinkedBlockingQueue();
    }

    // 同步信息
    public void sendMqSyncMessage(MqTopic mqTopic, String mes) throws Exception {
        sendMqSyncMessage(mqTopic, mes, false, false, false, null, false, 0);
    }

    // 同步严格消息顺序 [所有需要发送的orderId 需要相同]
    public void sendMqSyncMessageOrder(MqTopic mqTopic, String mes, String orderId) throws Exception {
        sendMqSyncMessage(mqTopic, mes, false, false, true, orderId, false, 0);
    }

    // 异步信息
    public void sendMqASyncMessage(MqTopic mqTopic, String mes) throws Exception {
        sendMqSyncMessage(mqTopic, mes, true, true, false, null, false, 0);
    }

    // 异步严格消息顺序 [所有需要发送的orderId 需要相同]
    public void sendMqASyncMessageOrder(MqTopic mqTopic, String mes, String orderId) throws Exception {
        sendMqSyncMessage(mqTopic, mes, true, true, true, orderId, false, 0);
    }

    // 异步定时任务 比如 30 分钟后发送出去 -- 会先进 延迟队列中 --需要等业务需求
    public void sendMqASyncMessageDelay(MqTopic mqTopic, String mes, int level) throws Exception {
        sendMqSyncMessage(mqTopic, mes, true, true, false, null, true, level);
    }

    //批量发送消息
    public void batch() {

    }

    // 异步事务消息
    public void sendMqASyncMessageTransaction() {

    }


    /**
     * mq发送同步消息
     *
     * @param mqTopic     主题
     * @param mes         消息内容
     * @param callback    (同步回调,百分百 保证消息可靠)
     * @param memoryRetry (内存 序列化)
     * @param order       订单
     * @param orderId     订单id
     * @throws Exception 异常
     */
    public void sendMqSyncMessage(MqTopic mqTopic, String mes, boolean callback,
                                  boolean memoryRetry, boolean order, String orderId, boolean delayTimeLevel, int level) throws Exception {
        assert mqTopic != null;
        assert StringUtils.isNotEmpty(mes);
        CustomerMessage message = new CustomerMessage();
        message.setOrder(order);
        message.setOrderId(orderId);
        message.setTopic(mqTopic.getTopic());
        message.setTags(mqTopic.getTags());
        message.setMes(mes);
        message.setKeyId(CommonUuidUtils.genericUuid());
        message.setCallback(callback);
        message.setMemoryRetry(memoryRetry);
//        现在RocketMq并不支持任意时间的延时，需要设置几个固定的延时等级，从1s到2h分别对应着等级1到18
//        private String messageDelayLevel = "1s 5s 10s 30s 1m 2m 3m 4m 5m 6m 7m 8m 9m 10m 20m 30m 1h 2h";
        if (delayTimeLevel) {
            message.setDelayTimeLevel(level);
        }
        sendMqSyncMessage(message);
    }


    /**
     * mq发送同步消息
     *
     * @param message 消息
     * @throws Exception 异常
     */
    private void sendMqSyncMessage(CustomerMessage message) throws Exception {
        // 设置了mess
        message.setBody(message.getMes().getBytes());
        // 消息丢失定位用的
        message.setKeys(message.getKeyId());
        // 异步
        if (message.isCallback()) {
            // 严格顺序发送
            CustomeSendCallback customeSendCallback = new CustomeSendCallback(message);
            if (message.isOrder()) {
                mqProducer.send(message, new CustomerMessageQueueSelector(), message.getOrderId(), customeSendCallback);
            } else {
                mqProducer.send(message, customeSendCallback);
            }
//            log.debug("获取返回信息类：{}",customeSendCallback);
        } else {
            if (message.isOrder()) {
                SendResult send = mqProducer.send(message, new CustomerMessageQueueSelector(), message.getOrderId());
                mqSyncResult(message, send);
            } else {
                SendResult send = mqProducer.send(message);
                mqSyncResult(message, send);
            }
        }
    }

    /**
     * mq同步结果
     *
     * @param message 消息
     * @param send    发送
     * @throws Exception 异常
     */
    private void mqSyncResult(CustomerMessage message, SendResult send) throws Exception {
        log.warn("mq 同步消息发送:{}", send.getSendStatus());
        if (send.getSendStatus() != SendStatus.SEND_OK) {
            mkdirMQFailFile(message);
        }
    }

    /**
     * mkdir mqfail文件
     *
     * @param customerMessage 客户留言
     * @throws Exception 异常
     */
    private void mkdirMQFailFile(Object customerMessage) throws Exception {
        File file_Json = getFile("mq_fail.json");
        FileWriter fileWriter = new FileWriter(file_Json, true);
        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(file_Json));

        // 需要回车换行
        bufferedWriter.write(JSON.toJSONString(customerMessage) + "\n\r");
        bufferedWriter.flush();
        bufferedWriter.close();
        fileWriter.close();
    }


    /**
     * 加载初始化资源
     *
     * @param fileName 文件名称
     * @param clazz    clazz
     * @return {@link Object}
     * @throws Exception 异常
     */
    private Object loadInitResource(String fileName, Class<?> clazz) throws Exception {
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        // 获取到了 target/classes 路径
        String path = Objects.requireNonNull(contextClassLoader.getResource("")).getPath();
        File file = new File(path + "mq/");
        if (!file.exists()) {
            return null;
        }
        File file_Json = new File(path + "mq/" + fileName);
        if (!file_Json.exists()) {
            return null;
        }

        BufferedReader br = new BufferedReader(new FileReader(file_Json));
        // 反序列化
        String s = br.readLine();
        return JSON.parseObject(s, clazz);
    }

    /**
     * mkdir serizted失败文件
     *
     * @param customerMessage 客户留言
     * @param fileName        文件名称
     * @throws Exception 异常
     */
    private void mkdirSeriztedFailFile(Object customerMessage, String fileName) throws Exception {
        File file_Json = getFile(fileName);
        FileWriter fileWriter = new FileWriter(file_Json);
        BufferedWriter bufferedWriter = new BufferedWriter(new FileWriter(file_Json, false));
        // 需要回车换行
        bufferedWriter.write(JSON.toJSONString(customerMessage));
        bufferedWriter.flush();
        bufferedWriter.close();
        fileWriter.close();
    }


    private File getFile(String fileName) throws IOException {
        ClassLoader contextClassLoader = Thread.currentThread().getContextClassLoader();
        // 获取到了 target/classes 路径
        String path = Objects.requireNonNull(contextClassLoader.getResource("")).getPath();
        File file = new File(path + "mq/");
        if (!file.exists()) {
            // 创建目录
            file.mkdir();
        }
        File file_Json = new File(path + "mq/" + fileName);
        if (!file_Json.exists()) {
            file_Json.createNewFile();
        }
        return file_Json;
    }


    @Override
    public void start() {

        threadPoolExecutor.execute(() -> {
            try {
                while (true) {
                    CustomerMessage take = linedBlock.take();
                    // 执行定时任务
                    threadPoolExecutor.scheduleAtFixedRate(new RocketMqRunnable(take), 5000, 5 * 60 * 1000,
                            TimeUnit.MILLISECONDS);
                }
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        });
    }

    @Override
    public void stop() {
        //停止了WEB 容器,把内存的内容 序列化进文件中
        try {
            mkdirSeriztedFailFile(linedBlock, "mq_queue_serializable.json");
            mkdirSeriztedFailFile(concurrentHashMapObject, "mq_map.json");
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Override
    public boolean isRunning() {
        return false;
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        // 启动需要加载 由于项目停止而生成的 json 数据
        Map map_json = (Map) loadInitResource("mq_map.json", concurrentHashMapObject.getClass());
        if (map_json != null) {
            for (Object me : map_json.entrySet()) {
                Map.Entry m = (Map.Entry) me;
                Object key = m.getKey();
                Map value = (Map) m.getValue();
                int count = (int) value.get("count");
                CustomerMessage customerMessage = new CustomerMessage();
                Map customerMessage1 = (Map) value.get("customerMessage");
                customerMessage.setTopic((String) customerMessage1.get("topic"));
                customerMessage.setKeyId((String) customerMessage1.get("keyId"));
                customerMessage.setTags((String) customerMessage1.get("tags"));
                customerMessage.setCallback((Boolean) customerMessage1.get("callback"));
                customerMessage.setMemoryRetry((Boolean) customerMessage1.get("memoryRetry"));
                customerMessage.setMes((String) customerMessage1.get("mes"));
                customerMessage.setOrder((Boolean) customerMessage1.get("order"));
                customerMessage.setOrderId((String) customerMessage1.get("orderId"));
                Map<String, String> properties = (Map<String, String>) customerMessage1.get("properties");
                customerMessage.addProperties(properties);
                RecursionMessage recursionMessage = new RecursionMessage(customerMessage, count);
                if (!StringUtils.isEmpty((CharSequence) key) && recursionMessage.getCustomerMessage() != null) {
                    concurrentHashMapObject.put((String) key, recursionMessage);
                }
            }
        }
        // 加载队列
        Object o = loadInitResource("mq_queue_serializable.json", linedBlock.getClass());
        if (o != null) {
            LinkedBlockingQueue linkedBlockingQueue = (LinkedBlockingQueue) o;
            Iterator iterator = linkedBlockingQueue.iterator();
            while (iterator.hasNext()) {
                Map next = (Map) iterator.next();
                CustomerMessage customerMessage = new CustomerMessage();
                customerMessage.setTopic((String) next.get("topic"));
                customerMessage.setKeyId((String) next.get("keyId"));
                customerMessage.setTags((String) next.get("tags"));
                customerMessage.setCallback((Boolean) next.get("callback"));
                customerMessage.setMemoryRetry((Boolean) next.get("memoryRetry"));
                customerMessage.setMes((String) next.get("mes"));
                customerMessage.setOrder((Boolean) next.get("order"));
                customerMessage.setOrderId((String) next.get("orderId"));
                Map<String, String> properties = (Map<String, String>) next.get("properties");
                customerMessage.addProperties(properties);
                if (!StringUtils.isEmpty(customerMessage.getMes())) {
                    linedBlock.offer(customerMessage);
                }
            }
        }
    }


    static class RecursionMessage implements Serializable {

        private CustomerMessage customerMessage;

        private Integer count;

        public RecursionMessage(CustomerMessage customerMessage, Integer count) {
            this.customerMessage = customerMessage;
            this.count = count;
        }

        public CustomerMessage getCustomerMessage() {
            return customerMessage;
        }

        public void setCustomerMessage(CustomerMessage customerMessage) {
            this.customerMessage = customerMessage;
        }

        public Integer getCount() {
            return count;
        }

        public void setCount(Integer count) {
            this.count = count;
        }
    }


    class RocketMqRunnable implements Runnable {

        private final CustomerMessage customerMessage;


        public RocketMqRunnable(CustomerMessage customerMessage) {
            this.customerMessage = customerMessage;
        }

        @Override
        public void run() {
            try {
                sendMqSyncMessage(customerMessage);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 严格消息服务器
     */
    static class CustomerMessageQueueSelector implements MessageQueueSelector {

        @Override
        public MessageQueue select(List<MessageQueue> mqs, Message msg, Object arg) {
            // 顺序ID
            String orderId = (String) arg;
            // 总共的队列数
            int size = mqs.size();
            // 取余 做 为运算符 肯定是小于 size
            int i = orderId.hashCode() % size & size;
            return mqs.get(i);
        }
    }


    class CustomeSendCallback implements SendCallback {

        private final CustomerMessage message;

        public CustomeSendCallback(CustomerMessage message) {
            this.message = message;
        }

        @Override
        public void onSuccess(SendResult sendResult) {
            MessageQueue messageQueue = sendResult.getMessageQueue();
            String brokerName = messageQueue.getBrokerName();
            String topic = messageQueue.getTopic();
            log.info("主题:{},消息发送brokerName:{}成功", topic, brokerName);
        }

        @Override
        public void onException(Throwable e) {
            log.error("主题:{},标签:{}消息发送失败", message.getTopic(), message.getTags());
            e.printStackTrace();
            // 内存重试机制,证数据完整
            if (message.isMemoryRetry()) {
                // 粒度最少,同一 String 对象 [String 常量池 内存指针是相同的]
                synchronized (message.getKeyId()) {
                    RecursionMessage recursionMessage = concurrentHashMapObject.get(message.getKeyId());
                    if (recursionMessage == null) {
                        recursionMessage = new RecursionMessage(message, 1);
                        concurrentHashMapObject.put(message.getKeyId(), recursionMessage);
                        linedBlock.offer(message);
                    } else {
                        int count = recursionMessage.count;
                        if (count > 3) {
                            // 需要把消息写成文件,持久化保存
                            try {
                                mkdirMQFailFile(message);
                            } catch (Exception ex) {
                                log.error("MQ消息存入出错,出错消息写入文件失败");
                            }
                            concurrentHashMapObject.remove(message.getKeyId());
                        } else {
                            count++;
                            recursionMessage.setCount(count);
                            concurrentHashMapObject.put(message.getKeyId(), recursionMessage);
                            linedBlock.offer(message);
                        }
                    }
                }
            }
        }

    }
}
