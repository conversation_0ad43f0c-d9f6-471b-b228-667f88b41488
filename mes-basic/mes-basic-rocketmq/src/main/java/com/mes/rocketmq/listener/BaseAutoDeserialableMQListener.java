package com.mes.rocketmq.listener;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.ParameterizedType;
import java.lang.reflect.Type;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * deserializable listener
 * TODO: 临时抽象类, 后续有空再改为动态代理接口, 不影响正常使用.
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Slf4j
public abstract class BaseAutoDeserialableMQListener<T>
        implements RocketMQListener<MessageExt>, AutoDeseriableMQListener<T> {

    private volatile Class<?> messageType = null;

    @Autowired
    private ObjectMapper objectMapper;


    @Override
    public void onMessage(MessageExt message) {
        @SuppressWarnings("unchecked") T body = (T) doConvertMessage(message);
        if (skipCondition(message, body)) {
            return;
        }
        onMessage(message, body);
    }

    public boolean skipCondition(MessageExt messageExt, T message) {
        return false;
    }

    protected final Class<?> getMessageType() {
        if (messageType == null) {
            synchronized (this) {
                if (messageType == null) {
                    messageType = doGetMessageType();
                }
            }
        }
        return messageType;
    }

    private Class<?> doGetMessageType() {
        Class<?> targetClass = AopProxyUtils.ultimateTargetClass(this);
        Type type = targetClass.getGenericSuperclass();
        if (type instanceof ParameterizedType) {
            ParameterizedType parameterizedType = (ParameterizedType) type;
            if (Objects.equals(parameterizedType.getRawType(), BaseAutoDeserialableMQListener.class)) {
                Type[] actualTypeArguments = parameterizedType.getActualTypeArguments();
                if (Objects.nonNull(actualTypeArguments) && actualTypeArguments.length > 0) {
                    Type typeArgument = actualTypeArguments[0];
                    // 泛型
                    if (typeArgument instanceof ParameterizedType) {
                        return (Class<?>) ((ParameterizedType) typeArgument).getRawType();
                    }
                    return (Class<?>) typeArgument;

                } else {
                    return Object.class;
                }
            }
        }
        return Object.class;
    }

    private Object doConvertMessage(MessageExt messageExt) {
        Class<?> messageType = getMessageType();
        if (Objects.equals(messageType, MessageExt.class)) {
            return messageExt;
        } else {
            String str = new String(messageExt.getBody(), StandardCharsets.UTF_8);
            if (Objects.equals(messageType, String.class)) {
                return str;
            } else {
                // If msgType not string, use objectMapper change it.
                try {
                    return objectMapper.readValue(str, messageType);
                } catch (Exception e) {
                    log.info("convert failed. str:{}, msgType:{}", str, messageType);
                    throw new RuntimeException("cannot convert message to " + messageType, e);
                }
            }
        }
    }

}
