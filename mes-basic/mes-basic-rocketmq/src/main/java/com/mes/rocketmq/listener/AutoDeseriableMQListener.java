package com.mes.rocketmq.listener;

import org.apache.rocketmq.common.message.MessageExt;

/**
 * 扩展{@link org.apache.rocketmq.spring.core.RocketMQListener}.
 * 保留{@link MessageExt}, 并基于泛型`T`自动反序列化`message`.
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
public interface AutoDeseriableMQListener<T> {

    /**
     * 在消息
     *
     * @param messageExt 消息完整信息
     * @param message    序列化后的消息 body
     * @see org.apache.rocketmq.spring.core.RocketMQListener#onMessage(Object)
     */
    void onMessage(MessageExt messageExt, T message);

}
