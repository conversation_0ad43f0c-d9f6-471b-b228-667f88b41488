package com.mes.rocketmq;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;

/**
 * 加载本地 配置文件
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@Configuration
@PropertySources({
        @PropertySource(name = "mq_location", value = {"classpath:local-mq.properties"}, encoding = "UTF-8"),
})
public class RocketMqEnvironmentConfig {
}
