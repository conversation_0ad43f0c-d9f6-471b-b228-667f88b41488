package com.mes.rocketmq;

import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.rocketmq.common.message.Message;

import java.lang.reflect.Field;
import java.util.Map;

/**
 * 自定义消息类
 *
 * <AUTHOR>
 * @date 2022/03/21
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CustomerMessage extends Message {


    private String keyId;

    private boolean callback;

    private boolean memoryRetry;


    /**
     * 保证顺序
     */
    private boolean order;

    private String orderId;

    private String mes;


    public void addProperties(Map<String, String> map) {
        try {
            Field properties = Message.class.getDeclaredField("properties");
            properties.setAccessible(true);
            properties.set(this, map);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
