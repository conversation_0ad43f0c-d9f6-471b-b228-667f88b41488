package com.dinglite.sharding.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.annotation.PropertySources;


/**
 * mybatis云环境
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@Configuration
@PropertySources({
        @PropertySource(name = "dynamic_datasource_location", value = {"classpath:local-datasource.properties"}, encoding = "UTF-8"),
        @PropertySource(name = "seata_location", value = {"classpath:local-seata.properties"}, encoding = "UTF-8")
})
public class MybatisCloudEnviroment {

}
