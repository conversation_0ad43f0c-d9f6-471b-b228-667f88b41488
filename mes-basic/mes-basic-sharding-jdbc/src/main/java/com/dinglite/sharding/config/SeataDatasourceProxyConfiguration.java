package com.dinglite.sharding.config;


import io.seata.rm.datasource.DataSourceProxy;
import org.apache.shardingsphere.driver.jdbc.adapter.AbstractDataSourceAdapter;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * seata数据源代理配置
 * 配置 Seata 与 动态数据源代理
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Configuration
@AutoConfigureAfter(AbstractDataSourceAdapter.class)
public class SeataDatasourceProxyConfiguration {


    @Bean
    @Primary
    public DataSourceProxy dataSourceProxy(DataSource dataSource) {
        return new DataSourceProxy(dataSource);
    }


}
