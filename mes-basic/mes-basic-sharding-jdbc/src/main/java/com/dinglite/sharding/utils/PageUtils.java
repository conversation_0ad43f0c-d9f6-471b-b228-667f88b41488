package com.dinglite.sharding.utils;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.common.global.PageParam;
import org.springframework.beans.BeanUtils;

import java.util.List;

public class PageUtils {

    /**
     * 自定义查询参数转IPage
     *
     * @param param
     * @return
     */
    public static <T> IPage<T> toPage(PageParam param) {
        return new Page<>(param.getCurrent(), param.getSize());
    }

    /**
     * 自定义查询参数转IPage
     *
     * @param param
     * @return
     */
    public static <T> IPage<T> toPage(PageParam param, Class<T> clazz) {
        Page<T> tPage = new Page<>(param.getCurrent(), param.getSize());
        return tPage;
    }


    /**
     * ipage转分页传输类
     *
     * @param iPage
     * @param <T>
     * @return
     */
    public static <T> PageDTO<T> toDTO(IPage<T> iPage) {
        PageDTO<T> pageDTO = new PageDTO<>();
        BeanUtils.copyProperties(iPage, pageDTO);
        return pageDTO;
    }


    /**
     * 手动分页
     *
     * @param current  当前页
     * @param size     页容量
     * @param elements 全部集合
     * @param <T>
     * @return
     */
    public static <T> IPage<T> toPage(Long current, Long size, List<T> elements) {
        int total = elements.size();
        Page<T> page = new Page<>(current, size);
        page.setTotal(total);

        long startIndex = size * (current - 1);
        long endIndex = size * current;

        if (endIndex > total) {
            endIndex = total;
        }

        if (startIndex > total) {
            startIndex = total;
        }

        List<T> subList = elements.subList((int) startIndex, (int) endIndex);
        page.setRecords(subList);
        return page;
    }
}
