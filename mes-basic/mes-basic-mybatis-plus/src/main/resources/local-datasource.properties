### 从nacos上拉取配置
spring.cloud.nacos.config.shared-dataids=mes-datasource.properties,mes-application.properties
### 默认数据源 dlt-sz.tpddns.cn
### mybatis-plus 扫描 Mapper xml
mybatis-plus.mapperLocations=classpath*:mapper/*.xml
### id 生成策略
mybatis-plus.global-config.db-config.id-type=ID_WORKER

# 不需要打印 Banner
mybatis-plus.global-config.banner=false

#mybatis开启逻辑删除
mybatis-plus.global-config.db-config.logic-delete-field=FLAG
mybatis-plus.global-config.db-config.logic-not-delete-value=N
mybatis-plus.global-config.db-config.logic-delete-value=Y

# 是否分层来处理租户
auto.lamination=false