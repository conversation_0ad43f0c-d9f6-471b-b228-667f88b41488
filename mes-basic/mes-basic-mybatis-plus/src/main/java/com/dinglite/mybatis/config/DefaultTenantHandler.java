package com.dinglite.mybatis.config;

import com.baomidou.mybatisplus.extension.plugins.tenant.TenantHandler;
import net.sf.jsqlparser.expression.Expression;
import net.sf.jsqlparser.expression.StringValue;

import java.util.ArrayList;
import java.util.List;


/**
 * 租户违约处理程序
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
public class DefaultTenantHandler implements TenantHandler {

    private boolean lamination;


    public DefaultTenantHandler(boolean lamination) {
        this.lamination = lamination;
    }

    public final static String TENANTCOLUMN = "TENANT_ID";

    // 不需要走租户集合
    private static List<String> filterTableName = new ArrayList<>();

    static {
        filterTableName.add("engine_field");
    }

    /**
     * select since: 3.3.2，参数 true 表示为 select 下的 where 条件,false 表示 insert/update/delete 下的条件
     *      * lect 下才允许多参(ValueListExpression),否则只支持单参
     *
     * @param where 在哪里
     * @return {@link Expression}
     */
    @Override
    public Expression getTenantId(boolean where) {
        String tenantId = null;
        // 代表不是分层
        if(!lamination) {
            // tenantId = AuthenticationServletUtils.getTenantId();
        }
        return new StringValue(tenantId);
    }

    @Override
    public String getTenantIdColumn() {
        return TENANTCOLUMN;
    }

    @Override
    public boolean doTableFilter(String tableName) {
        return filterTableName.contains(tableName);
    }
}
