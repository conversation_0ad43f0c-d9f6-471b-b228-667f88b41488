package com.dinglite.mybatis.config;

import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import io.seata.rm.datasource.DataSourceProxy;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;

/**
 * seata数据源代理配置
 * 配置 Seata 与 动态数据源代理
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Configuration
@AutoConfigureAfter(DynamicDataSourceAutoConfiguration.class)
public class SeataDatasourceProxyConfiguration {


    @Bean
    @Primary
    public DataSourceProxy dataSourceProxy(DataSource dataSource) {
        return new DataSourceProxy(dataSource);
    }


}
