package com.dinglite.hbase.test;

import lombok.extern.slf4j.Slf4j;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.HBaseConfiguration;
import org.apache.hadoop.hbase.NamespaceDescriptor;
import org.apache.hadoop.hbase.NamespaceExistException;
import org.apache.hadoop.hbase.TableName;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.util.Bytes;

import java.io.IOException;


/**
 * hbase测试
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@Slf4j
public class HbaseTest {

    private static Connection connection;
    private static Admin admin;

    static {
        try {
            Configuration configuration = HBaseConfiguration.create();
            //centos8 要在host文件中添加
//            configuration.set("hbase.zookeeper.quorum", "centos8");
            configuration.set("hbase.zookeeper.quorum", "dlt-sz.tpddns.cn");
            connection = ConnectionFactory.createConnection(configuration);
            admin = connection.getAdmin();
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    /**
     * 判断表是否存在
     *
     * @param tableName 表名
     * @return boolean
     * @throws IOException ioexception
     */
    public static boolean isTableExists(String tableName) throws IOException {
        return admin.tableExists(TableName.valueOf(tableName));
    }


    /**
     * 创建表
     *
     * @param tableName 表名
     * @param args      arg游戏
     * @throws IOException ioexception
     */
    public static void createTable(String tableName, String... args) throws IOException {
        if (args.length == 0) {
            throw new RuntimeException("请设置列族");
        }
        if (isTableExists(tableName)) {
            throw new RuntimeException("表已存在");
        }
        TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
//        HTableDescriptor hTableDescriptor = new HTableDescriptor(TableName.valueOf(tableName));
        for (String arg : args) {
//            HColumnDescriptor hColumnDescriptor = new HColumnDescriptor(arg);
//            hTableDescriptor.addFamily(hColumnDescriptor);
            ColumnFamilyDescriptor columnFamilyDescriptor = ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes(arg)).build();
            tableDescriptorBuilder.setColumnFamily(columnFamilyDescriptor);
        }
        TableDescriptor tableDescriptor = tableDescriptorBuilder.build();
        admin.createTable(tableDescriptor);
    }

    /**
     * 删除表
     *
     * @param tableName 表名
     * @throws IOException ioexception
     */
    public static void deleteTable(String tableName) throws IOException {
        TableName tableName1 = TableName.valueOf(tableName);
        admin.disableTable(tableName1);
        admin.deleteTable(tableName1);
    }

    /**
     * 创建名称空间
     *
     * @param ns ns
     */
    public static void createNameSpace(String ns) {
        NamespaceDescriptor namespaceDescriptor = NamespaceDescriptor.create(ns).build();
        try {
            admin.createNamespace(namespaceDescriptor);
        } catch (NamespaceExistException e) {
            throw new RuntimeException(ns + "命令已存在");
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 插入数据
     *
     * @param tableName 表名
     * @param rk        rowKey
     * @param cf        列族
     * @param cn        列名
     * @param value     值
     * @throws IOException ioexception
     */
    public static void put(String tableName, String rk, String cf, String cn, String value) throws IOException {
        Table table = connection.getTable(TableName.valueOf(tableName));
        Put put = new Put(Bytes.toBytes(rk));
        put.addColumn(Bytes.toBytes(cf), null, Bytes.toBytes(value));
        table.put(put);
        table.close();
    }

    public static void get(String tableName, String rk, String cf, String cn) throws IOException {
        Table table = connection.getTable(TableName.valueOf(tableName));
        Get get = new Get(Bytes.toBytes(rk));
        Result result = table.get(get);
//        for (Cell cell : result.rawCells()) {
//        }
        table.close();
    }

    /**
     * 关闭资源
     *
     * @throws IOException ioexception
     */
    public static void close() throws IOException {
        if (admin != null) {
            admin.close();
        }
        if (connection != null) {
            connection.close();
        }
    }

    public static void main(String[] args) throws IOException {
        System.out.println(isTableExists("teacher"));
//        createTable("teacher3", "info1", "info2");
//        deleteTable("teacher2");
//        System.out.println(isTableExists("teacher2"));

//        createNameSpace("testSpace");
//        createTable("testSpace:stu", "info1", "info2");

//        put("teacher", "1002", "info1", "name", "zhangsan");
        close();
    }
}
