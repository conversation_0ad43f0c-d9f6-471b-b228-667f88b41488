package com.dinglite.hbase.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONException;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.hbase.*;
import org.apache.hadoop.hbase.client.*;
import org.apache.hadoop.hbase.filter.*;
import org.apache.hadoop.hbase.util.Bytes;
import org.springframework.stereotype.Component;

import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @Description
 * <AUTHOR>
 * @Date 2022-10-17 10:39
 */
@Component
public class HBaseUtil {
    //    private static final String QUORUM = "*************";
    private static final String QUORUM = "dlt-sz.tpddns.cn";
    private static final String CLIENT_PORT = "2181";
    private static Connection conn;

    public static synchronized Connection getConnection() {
        if (conn == null) {
            try {
                Configuration conf = HBaseConfiguration.create();
                conf.set("hbase.zookeeper.quorum", QUORUM);
                conf.set("hbase.zookeeper.property.clientPort", CLIENT_PORT);
                conf.set("hbase.client.pause", "50");
                conf.set("hbase.client.retries.number", "21");
                conf.set("hbase.client.operation.timeout", "5000");
                conn = ConnectionFactory.createConnection(conf);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return conn;
    }


    public static String getRowKey(List<String> list) {
        StringBuilder rowKey = new StringBuilder();
        for (String s : list) {
            rowKey.append(s);
        }
        return rowKey.toString();
    }

    public static String formatJsonValue(Map<String, String> map) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.putAll(map);
        return "" + jsonObject;
    }

    public static String formatJsonValueByGson(Map<String, Object> map) {
        Gson gson = new Gson();
        return gson.toJson(map);
    }

    public static void createTable(String tableName, String... args) {
        conn = getConnection();
        System.out.println("start create table ......");

        try {
            Admin admin = conn.getAdmin();
            if (admin.tableExists(TableName.valueOf(tableName))) {
                admin.disableTable(TableName.valueOf(tableName));
                admin.deleteTable(TableName.valueOf(tableName));
                System.out.println(tableName + " is exist,detele....");
            }
            TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
            for (String arg : args) {
                ColumnFamilyDescriptor columnFamilyDescriptor = ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes(arg)).build();
                tableDescriptorBuilder.setColumnFamily(columnFamilyDescriptor);
            }
            TableDescriptor tableDescriptor = tableDescriptorBuilder.build();

            admin.createTable(tableDescriptor);
            admin.close();
        } catch (IOException var3) {
            var3.printStackTrace();
        }

        System.out.println("end create table ......");
    }

    public static void insertDataS(String tableName, String familyName, Map<String, String> rowkeyMap) {
        conn = getConnection();
        List<Put> puts = new ArrayList<>();

        for (Map.Entry<String, String> stringStringEntry : rowkeyMap.entrySet()) {
            Put put = new Put(Bytes.toBytes(stringStringEntry.getKey()));
            put.addColumn(Bytes.toBytes(familyName), "logValue".getBytes(), Bytes.toBytes(stringStringEntry.getValue()));
            puts.add(put);
        }

        try {
            Table table = conn.getTable(TableName.valueOf(tableName));
            table.put(puts);
            table.close();
        } catch (IOException var7) {
            var7.printStackTrace();
        }

    }

    public static void addFamily(String tableName, String familyName) {
        conn = getConnection();
        System.out.println("start addFamily ......");

        try {
            Admin admin = conn.getAdmin();
            TableDescriptorBuilder tableDescriptorBuilder = TableDescriptorBuilder.newBuilder(TableName.valueOf(tableName));
            ColumnFamilyDescriptor columnFamilyDescriptor = ColumnFamilyDescriptorBuilder.newBuilder(Bytes.toBytes(familyName)).build();
            tableDescriptorBuilder.setColumnFamily(columnFamilyDescriptor);
            TableDescriptor tableDescriptor = tableDescriptorBuilder.build();
            admin.disableTable(TableName.valueOf(tableName));
            admin.modifyTable(tableDescriptor);
            admin.enableTable(TableName.valueOf(tableName));
            admin.close();
        } catch (IOException var5) {
            var5.printStackTrace();
        }

        System.out.println("end addFamily ......");
    }


    /**
     * 插入数据
     *
     * @param tableName  表名
     * @param familyName 姓
     * @param rowkey     rowkey
     * @param jsonObj    json obj
     * @return boolean
     */
    public static boolean insertData(String tableName, String familyName, String rowkey, String jsonObj) {
        conn = getConnection();
        Put put = new Put(Bytes.toBytes(rowkey));
        put.addColumn(Bytes.toBytes(familyName), null, Bytes.toBytes(jsonObj));

        try {
            Table table = conn.getTable(TableName.valueOf(tableName));
            table.put(put);
            table.close();
        } catch (IOException var7) {
            var7.printStackTrace();
        }

        boolean flag = false;
        String result = findByRowkey(tableName, rowkey);
        if (result != null) {
            flag = true;
        }

        return flag;
    }

    public static void insertDataS(String tableName) {
        conn = getConnection();
        try {
            Table table = conn.getTable(TableName.valueOf(tableName));
            table.close();
        } catch (IOException var2) {
            var2.printStackTrace();
        }

    }

    public static void delete(String tableName, String rowkey) {
        conn = getConnection();
        Delete delete = new Delete(Bytes.toBytes(rowkey));

        try {
            Table table = conn.getTable(TableName.valueOf(tableName));
            table.delete(delete);
            table.close();
        } catch (IOException var4) {
            var4.printStackTrace();
        }

    }


    /**
     * 通过rowkey找到的
     *
     * @param tableName 表名
     * @param rowkey    rowkey
     * @return {@link String}
     */
    public static String findByRowkey(String tableName, String rowkey) {
        conn = getConnection();
        String value = null;
        Table table;
        try {
            table = conn.getTable(TableName.valueOf(tableName));
            Get scan = new Get(rowkey.getBytes());
            Result r = table.get(scan);
            Cell[] cells = r.rawCells();
            for (Cell cell : cells) {
                value = Bytes.toString(CellUtil.cloneValue(cell));
            }
            table.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return value;
    }


    public static JSONObject findOneByRowkey(String tableName, String rowkey) {
        long startTime = System.currentTimeMillis();
        conn = getConnection();
        JSONObject myJsonObject = null;
        try {
            Table table = conn.getTable(TableName.valueOf(tableName));
            Get scan = new Get(rowkey.getBytes());
            Result r = table.get(scan);
            Cell[] cells = r.rawCells();
            for (Cell cell : cells) {
                String value = Bytes.toString(CellUtil.cloneValue(cell));
                System.out.println("列：" + Bytes.toString(CellUtil.cloneFamily(cell)) + "====值:" + value);
                myJsonObject = JSONObject.parseObject(value);
            }
        } catch (Exception var14) {
            var14.printStackTrace();
        }

        long endTime = System.currentTimeMillis();
        float excTime = (float) (endTime - startTime);
        System.out.println("执行时间：" + excTime + "millisecond");
        return myJsonObject;
    }

    public static JSONArray findFilter(String tableName, List<Map<String, String>> list) throws JSONException {
        conn = getConnection();
        Scan scan = new Scan();
        FilterList filterList = new FilterList();

        for (Map<String, String> map : list) {
            String param = map.get("param");
            String type = map.get("type");
            RowFilter filter;
            if ("=".equals(type)) {
                filter = new RowFilter(CompareOperator.EQUAL, new SubstringComparator(param));
                filterList.addFilter(filter);
            } else if ("x=".equals(type)) {
                filter = new RowFilter(CompareOperator.EQUAL, new BinaryPrefixComparator(param.getBytes()));
                filterList.addFilter(filter);
            } else if (">=".equals(type)) {
                filter = new RowFilter(CompareOperator.GREATER_OR_EQUAL, new BinaryComparator(param.getBytes()));
                filterList.addFilter(filter);
            } else if ("<=".equals(type)) {
                filter = new RowFilter(CompareOperator.LESS_OR_EQUAL, new BinaryComparator(param.getBytes()));
                filterList.addFilter(filter);
            }
        }

        scan.setFilter(filterList);
        JSONArray jsonArray = new JSONArray();

        try {
            ResultScanner rs = conn.getTable(TableName.valueOf(tableName)).getScanner(scan);
            for (Result r : rs) {
                Cell[] cells = r.rawCells();
                for (Cell cell : cells) {
                    JSONObject myJsonObject = JSONObject.parseObject(Bytes.toString(CellUtil.cloneValue(cell)));
                    myJsonObject.put("rowKey", new String(r.getRow()));
                    jsonArray.add(myJsonObject);
                    System.out.println(myJsonObject);
                }
            }
        } catch (IOException var14) {
            var14.printStackTrace();
        }

        return jsonArray;
    }


    /**
     * 关闭资源
     */
    @PreDestroy
    public void doDestroy() {
        System.out.println("hbase连接要关闭啦");
        if (conn != null) {
            try {
                conn.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void main(String[] args) {
//        insertData("teacher", "info3", "1001", "{\"id\":\"1\",\"name\":\"hlc\"}");
//        System.out.println(findOneByRowkey("teacher", "1001"));

//        List<Map<String,String>> list=new ArrayList<>();
//        Map<String,String> map=new HashMap<>();
//        map.put("param","1001");
//        map.put("type","=");
//        list.add(map);
//        System.out.println(findFilter("teacher",list));


//        createTable("data_01","logValue");
//        createTable("data_02","logValue");
//        createTable("data_03","logValue");
//        createTable("data_04","logValue");
//        createTable("data_05","logValue");
//        createTable("data_06","logValue");
//        createTable("data_07","logValue");
//        createTable("data_08","logValue");
//        createTable("data_09","logValue");
//        createTable("exception_01","logValue");
//        createTable("exception_02","logValue");
//        createTable("exception_03","logValue");
//        createTable("exception_04","logValue");
//        createTable("exception_05","logValue");


    }

}
