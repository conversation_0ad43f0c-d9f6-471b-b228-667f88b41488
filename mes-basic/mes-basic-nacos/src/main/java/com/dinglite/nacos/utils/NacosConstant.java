package com.dinglite.nacos.utils;

import org.springframework.core.env.Environment;

/**
 * 配置常量
 *
 * *
 */
public class NacosConstant {

    public final static  String DEFAULT_USER_NAME = "default";

    public final static  String MES_USER_NAME = "mes";

    public static class  DefaultUserName {

        // 默认的 地址
        public static String DEFAULT_NACOS_ADDR = "127.0.0.1:8848";

        // 默认的 名称空间
        public static  String DEFAULT_NACOS_NAMESPACE = "9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05";

        //默认的组
        public  static  String DEFAULT_NACOS_GROUP = "DEFAULT_GROUP";

    }


    public static class Mes  extends DefaultUserName {

        static {
            DEFAULT_NACOS_NAMESPACE = "9b2b60dc-f976-48ac-b6e2-e8b2c9dd8c05";
        }

    }


    public static DefaultUserName getDefaultUserName(Environment environment) {
        String uname = environment.getProperty("namespace", "default");
        DefaultUserName defaultUserName = null;
        switch (uname) {
            case NacosConstant.DEFAULT_USER_NAME :
                defaultUserName = new DefaultUserName();
                break;
            case NacosConstant.MES_USER_NAME :
                defaultUserName = new Mes();
                break;
            default:
                defaultUserName = new Mes();
        }
        return defaultUserName;
    }

}
