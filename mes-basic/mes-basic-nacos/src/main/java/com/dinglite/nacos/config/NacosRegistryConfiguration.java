package com.dinglite.nacos.config;

import com.dinglite.nacos.utils.NacosConstant;
import org.springframework.cloud.alibaba.nacos.NacosDiscoveryProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

/**
 * nacos注册中心配置
 *
 * <AUTHOR>
 * @date 2022/03/17
 */
@Configuration
public class NacosRegistryConfiguration {

    @Bean
    public NacosDiscoveryProperties nacosDiscoveryProperties(Environment environment) {
        NacosConstant.DefaultUserName defaultUserName = NacosConstant.getDefaultUserName(environment);
        NacosDiscoveryProperties nacosDiscoveryProperties = new NacosDiscoveryProperties();
        nacosDiscoveryProperties.setServerAddr(defaultUserName.DEFAULT_NACOS_ADDR);
        nacosDiscoveryProperties.setNamespace(defaultUserName.DEFAULT_NACOS_NAMESPACE);
        return nacosDiscoveryProperties;
    }

}
