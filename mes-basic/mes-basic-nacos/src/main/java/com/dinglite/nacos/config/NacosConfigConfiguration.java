package com.dinglite.nacos.config;

import com.dinglite.nacos.utils.NacosConstant;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cloud.alibaba.nacos.NacosConfigBootstrapConfiguration;
import org.springframework.cloud.alibaba.nacos.NacosConfigProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.lang.NonNull;

import java.lang.reflect.Method;


/**
 *  * 配置  --> Nacos 配置中心 原理 是通过 SpringCloud  BootstarpApplication 来完成注册的
 *  *  * NacosConfigService  来实现的  包含了 ServerListManager (通过配置文件来完成)
 *  *  * ClientWork  初始话newScheduledThreadPool (定时任务) 来10ms 来检查配置文件是否更新
 *  *  * newCachedThreadPool 创建了线程池 来实现长轮询,当有任务的时候就开启
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@Configuration
@RequiredArgsConstructor
public class NacosConfigConfiguration implements BeanPostProcessor {

    private final Environment environment;

    private static final Method NACOS_CONFIG_PROPERTIES_METHOD;

    static {
        try {
            NACOS_CONFIG_PROPERTIES_METHOD = NacosConfigBootstrapConfiguration.class.getDeclaredMethod("nacosConfigProperties");
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public Object postProcessBeforeInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        if (bean instanceof NacosConfigBootstrapConfiguration) {
            return createNacosConfigBootstrapConfigurationProxy((NacosConfigBootstrapConfiguration) bean, environment);
        }
        return bean;
    }

    /**
     * 创建 {@link NacosConfigBootstrapConfiguration} 的代理类
     * <p>重写了方法 {@link NacosConfigBootstrapConfiguration#nacosConfigProperties()}
     */
    private static NacosConfigBootstrapConfiguration createNacosConfigBootstrapConfigurationProxy(
            NacosConfigBootstrapConfiguration delegate, Environment environment) {
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(NacosConfigBootstrapConfiguration.class);
        enhancer.setCallback((MethodInterceptor) (obj, method, args, methodProxy) -> {

            // 重写方法 nacosConfigProperties()
            if (method.equals(NACOS_CONFIG_PROPERTIES_METHOD)) {
                NacosConstant.DefaultUserName defaultUserName = NacosConstant.getDefaultUserName(environment);
                NacosConfigProperties nacosConfigProperties = new NacosConfigProperties();
                nacosConfigProperties.setServerAddr(defaultUserName.DEFAULT_NACOS_ADDR);
                nacosConfigProperties.setNamespace(defaultUserName.DEFAULT_NACOS_NAMESPACE);
                nacosConfigProperties.setGroup(defaultUserName.DEFAULT_NACOS_GROUP);
//                nacosConfigProperties.setSharedDataids("mes-application.properties");
                return nacosConfigProperties;
            }

            return method.invoke(delegate, args);
        });
        return (NacosConfigBootstrapConfiguration) enhancer.create();
    }
}
