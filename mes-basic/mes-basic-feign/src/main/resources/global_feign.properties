#feign客户端建立连接超时时间
#feign.client.config.default.connect-timeout=10000

#feign客户端建立连接后读取资源超时时间
#feign.client.config.default.read-timeout=10000

# 与 Hystrix 集成
feign.hystrix.enabled=true
# OkHttp3 集成
feign.okhttp.enabled=true

#配置Hystrix 超时时间设置  true->开启  false->关闭
#hystrix.command.default.execution.timeout.enabled=true

#超时时间（默认1000ms）在调用方配置，被该调用方的所有方法的超时时间都是该值，优先级低于下边的指定配置
#hystrix.command.default.execution.isolation.thread.timeoutInMilliseconds=10000

#在调用方配置，被该调用方的指定方法（HystrixCommandKey方法名）的超时时间是该值
#hystrix.command.HystrixCommandKey.execution.isolation.thread.timeoutInMilliseconds=400000

#线程池核心线程数 默认为10
hystrix.threadpool.default.coreSize=20

#最大排队长度。默认-1  如果要从-1换成其他值则需重启，即该值不能动态调整，若要动态调整，需要使用到下边这个配置
hystrix.threadpool.default.maxQueueSize=100

#排队线程数量阈值，默认为5，达到时拒绝，如果配置了该选项，队列的大小是该队列
#hystrix.threadpool.default.queueSizeRejectionThreshold=5
# 简言之，10s内请求失败数量达到20个，断路器开。  当在配置时间窗口内达到此数量的失败后，进行短路。默认20个
hystrix.command.default.circuitBreaker.requestVolumeThreshold=20
#短路多久以后开始尝试是否恢复，默认5s
hystrix.command.default.circuitBreaker.sleepWindowInMilliseconds=5000
#出错百分比阈值，当达到此阈值后，开始短路。默认50%
hystrix.command.default.circuitBreaker.errorThresholdPercentage=50%
#调用线程允许请求HystrixCommand.GetFallback()的最大数量，默认10。超出时将会有异常抛出，注意：该项配置对于THREAD隔离模式也起作用
#hystrix.command.default.fallback.isolation.semaphore.maxConcurrentRequests=50000
logging.level.com.alibaba=error
#ribbon.ReadTimeout=10000
ribbon.ReadTimeout=20000
ribbon.ConnectTimeout=10000