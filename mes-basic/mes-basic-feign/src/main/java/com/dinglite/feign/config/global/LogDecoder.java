package com.dinglite.feign.config.global;


import com.dinglite.common.threadlocal.FeignContextHolder;
import com.dinglite.common.utils.CommonUtils;
import feign.FeignException;
import feign.Response;
import feign.codec.DecodeException;
import feign.codec.Decoder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Collection;
import java.util.LinkedList;

/**
 * 执行时间日志解码器
 */
@Slf4j
public class LogDecoder  implements Decoder {

    private Decoder delegate;



    public LogDecoder(Decoder delegate) {
        this.delegate = delegate;

    }

    @Override
    public Object decode(Response response, Type type) throws IOException, DecodeException, FeignException {
        Collection<String> collection = response.headers().get(FeignContextHolder.JlFeignRequestHeads.response_system);
        LinkedList<String> linkedList = new LinkedList<>(collection);
        if (CommonUtils.isEmpty(linkedList)) {
            return delegate.decode(response, type);
        }
        String accept = linkedList.get(0);
        FeignContextHolder.Feignmonitor feignmonitor = FeignContextHolder.get();
        String uid = feignmonitor.getUid();
        String puid = feignmonitor.getPuid();
        String path = feignmonitor.getPath();
        long start = feignmonitor.getStart();
        String method = feignmonitor.getMethod();
        String transfer = feignmonitor.getTransfer();


        log.warn("id:{} pid:{} 访问路径为:{} 访问方法为:{} 发起者为:{}  接受方:{}  访问时间为:{}s"
                ,uid,puid,path,method,transfer,accept,(System.currentTimeMillis()-start)/1000);
        return delegate.decode(response,type);
    }

}
