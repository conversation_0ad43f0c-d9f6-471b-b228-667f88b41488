package com.dinglite.feign.config.global;


import com.dinglite.common.constant.auth.AuthConstant;
import com.dinglite.common.global.AuthUser;
import com.dinglite.common.threadlocal.AuthUserContext;
import feign.RequestInterceptor;
import feign.RequestTemplate;

/**
 * 认证用户拦截器
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
public class JlAuthUserInterceptor implements RequestInterceptor {

    @Override
    public void apply(RequestTemplate template) {
        AuthUser authUser = AuthUserContext.get();
        template.header(AuthConstant.REQUEST_HEAD_AUTH,
                authUser != null
                        ? AuthUser.serialize(authUser)
                        : null);
    }


}
