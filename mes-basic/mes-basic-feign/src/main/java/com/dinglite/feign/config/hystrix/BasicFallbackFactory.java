package com.dinglite.feign.config.hystrix;

import feign.hystrix.FallbackFactory;
import lombok.AllArgsConstructor;
import org.springframework.cglib.proxy.Enhancer;

/**
 * ApplicationContextAware  属于 每个一 ApiContext
 * @param <T>
 */
@AllArgsConstructor
public class BasicFallbackFactory<T> implements FallbackFactory<T> {

    private Class<?> clazz;

    // 每次都会抛出异常
    @Override
    public T create(Throwable cause) {
        Enhancer enhancer = new Enhancer();
        enhancer.setSuperclass(clazz);
        enhancer.setUseCache(true);
        enhancer.setCallback(new BasicFeignFailCallback(cause));
        return (T) enhancer.create();
    }

}
