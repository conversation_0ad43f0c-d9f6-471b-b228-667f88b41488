package com.dinglite.feign.config.hystrix;


import com.dinglite.common.configuration.ConfigurationPropertyPrefixConstance;
import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.HashMap;
import java.util.Map;


@ConfigurationProperties(prefix = ConfigurationPropertyPrefixConstance.HYSTRIX_PATH)
public class CustomerHystrixCommandProperties {

    // 默认是5秒
    public static boolean timeout_enable = true;

    //客户端超时配置
    public static Map<String,Integer> clientCommandKeyTimeout = new HashMap<>();

    // 超时时间
//    public static int timeout_mills =  400000;
    public static int timeout_mills =  10000;

    // 执行策略
    public static String execution_strategy = "THREAD";

    // 线程池 核心线程数
    public  static int threadPool_coresize = 5;

    //  最大队列
    public static  int threadQueue_num = 10;

    public static String circuitBreaker_fail_percentage =  "50%";


    public  boolean isTimeout_enable() {
        return timeout_enable;
    }

    public  void setTimeout_enable(boolean timeout_enable) {
        CustomerHystrixCommandProperties.timeout_enable = timeout_enable;
    }

    public  int getTimeout_mills() {
        return timeout_mills;
    }

    public  void setTimeout_mills(int timeout_mills) {
        CustomerHystrixCommandProperties.timeout_mills = timeout_mills;
    }

    public  String getExecution_strategy() {
        return execution_strategy;
    }

    public  void setExecution_strategy(String execution_strategy) {
        CustomerHystrixCommandProperties.execution_strategy = execution_strategy;
    }

    public  int getThreadPool_coresize() {
        return threadPool_coresize;
    }

    public  void setThreadPool_coresize(int threadPool_coresize) {
        CustomerHystrixCommandProperties.threadPool_coresize = threadPool_coresize;
    }

    public  int getThreadQueue_num() {
        return threadQueue_num;
    }

    public  void setThreadQueue_num(int threadQueue_num) {
        CustomerHystrixCommandProperties.threadQueue_num = threadQueue_num;
    }

    public  String getCircuitBreaker_fail_percentage() {
        return circuitBreaker_fail_percentage;
    }

    public  void setCircuitBreaker_fail_percentage(String circuitBreaker_fail_percentage) {
        CustomerHystrixCommandProperties.circuitBreaker_fail_percentage = circuitBreaker_fail_percentage;
    }

    public Map<String, Integer> getClientCommandKeyTimeout() {
        return clientCommandKeyTimeout;
    }
}
