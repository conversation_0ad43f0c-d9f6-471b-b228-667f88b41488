package com.dinglite.feign.config.execption;

import feign.FeignException;

/**
 *
 * <AUTHOR>
 * @date 2022/03/17
 */
public class BaseFeignException extends FeignException {

    private int status;

    private String message;



    public BaseFeignException(int status, String message) {
        super(status, message);
        this.status =status;
        this.message = message;
    }


    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    @Override
    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

}
