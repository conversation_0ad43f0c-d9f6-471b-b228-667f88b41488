package com.dinglite.feign.config.hystrix;



import com.dinglite.feign.config.execption.BaseFeignException;
import com.netflix.hystrix.exception.HystrixTimeoutException;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

import java.lang.reflect.Method;

/**
 * 基本假装失败回调
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@AllArgsConstructor
@Slf4j
public class  BasicFeignFailCallback implements MethodInterceptor {


    private Throwable cause;

    /**
     * 拦截
     * 服务降级  // 超时的方法   // 直接返回 Global.fail
     *
     * @param o           目标对象
     * @param method      目标方法
     * @param methodProxy 代理方法
     * @param args        arg游戏
     * @return {@link Object}
     * @throws Throwable throwable
     */
    @Override
    public Object intercept(Object o, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
        // 获取到的方法
       if(cause instanceof HystrixTimeoutException) {
            String message = cause.getMessage();
            // 方法名称
            String methodName = method.getName();
            // 类名
            String name = method.getDeclaringClass().getName();
            cause = new BaseFeignException(708,"Feign服务超时执行类名"+name +"方法名"+methodName);
        }
        throw cause;
    }
}
