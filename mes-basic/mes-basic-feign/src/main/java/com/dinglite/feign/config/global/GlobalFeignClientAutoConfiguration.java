package com.dinglite.feign.config.global;

import com.dinglite.feign.config.hystrix.CustomerHystrixCommandProperties;
import com.dinglite.feign.config.hystrix.JlHystrixConfigure;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.codec.Decoder;
import feign.codec.ErrorDecoder;
import feign.optionals.OptionalDecoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.JlFeignClientSpecification;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.core.env.Environment;


/**
 * 全球假装端自动配置
 *
 * <AUTHOR>
 * @date 2022/03/17
 */
@Configuration
@EnableConfigurationProperties(CustomerHystrixCommandProperties.class)
public class GlobalFeignClientAutoConfiguration {

    private static String specificationName = "default.";

    private static Class<?>[] aClass = new Class[]{GlobalFeignConfigure.class};

    /**
     * 配置了全局的 FeignClientSpecifition
     *
     * @return {@link JlFeignClientSpecification}
     */
    @Bean
    public JlFeignClientSpecification jlFeignClientSpecifition(){
        String name = specificationName+JlFeignClientSpecification.class.getName();
        return new JlFeignClientSpecification(name,aClass);
    }

    /**
     * jl hystrix配置
     * 配置 全局 Hystrix Plus
     *
     * @param customerHystrixCommandProperties 客户hystrix命令属性
     * @return {@link JlHystrixConfigure}
     */
    @Bean
    public JlHystrixConfigure jlHystrixConfigure(CustomerHystrixCommandProperties customerHystrixCommandProperties) {
        return new JlHystrixConfigure(customerHystrixCommandProperties);
    }


    /**
     * 全球装配置
     *
     * <AUTHOR>
     * @date 2022/03/17
     */
    @Configuration
    public class GlobalFeignConfigure {

        @Bean
        public JlFeignLogRequestInterceptor jlFeignLogRequestInterceptor(Environment environment) {
            return new JlFeignLogRequestInterceptor(environment);
        }

        @Bean
        public JlAuthUserInterceptor jlAuthUserInterceptor() {
            return new JlAuthUserInterceptor();
        }

        
        @Bean
        @Primary
        public Decoder decoder(ObjectFactory<HttpMessageConverters> messageConverters) {
            return new LogDecoder(new OptionalDecoder(new ResponseEntityDecoder(new SpringDecoder(messageConverters))));
        }


        @Bean
        @Primary
        public ErrorDecoder errorDecoder(ObjectMapper objectMapper) {
            return new JlErrorDecoder(objectMapper);
        }

    }

}
