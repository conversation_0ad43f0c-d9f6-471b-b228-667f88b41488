package com.dinglite.feign.config.hystrix;


import com.dinglite.common.global.AuthUser;
import com.dinglite.common.threadlocal.AuthUserContext;
import com.dinglite.common.threadlocal.FeignContextHolder;
import com.netflix.hystrix.HystrixCommandKey;
import com.netflix.hystrix.HystrixCommandProperties;
import com.netflix.hystrix.strategy.HystrixPlugins;
import com.netflix.hystrix.strategy.concurrency.HystrixConcurrencyStrategy;
import com.netflix.hystrix.strategy.properties.HystrixPropertiesStrategy;

import java.util.Map;
import java.util.concurrent.Callable;

/**
 * 添加了 Hystrix 中一些插件
 */
public class JlHystrixConfigure {

    private String HYSTRIX_GATEWAY_COMMANDKEY = "CompositeDiscoveryClient";


    public JlHystrixConfigure(CustomerHystrixCommandProperties customerHystrixCommandProperties) {
        HystrixPlugins instance = HystrixPlugins.getInstance();
        // 如果有了
        HystrixConcurrencyStrategy concurrencyStrategy = instance.getConcurrencyStrategy();
        HystrixPropertiesStrategy propertiesStrategy = instance.getPropertiesStrategy();
        if(concurrencyStrategy != null || propertiesStrategy != null) {
            HystrixPlugins.reset();
        }
        HystrixPlugins.getInstance().registerConcurrencyStrategy(new JlHystrixConcurrentStrategy(concurrencyStrategy));
        HystrixPlugins.getInstance().registerPropertiesStrategy(new JlDynamicHystrixPropertiesStrategy(customerHystrixCommandProperties,propertiesStrategy));
    }


    public class JlHystrixConcurrentStrategy extends HystrixConcurrencyStrategy {

        private  HystrixConcurrencyStrategy delegate;

        public JlHystrixConcurrentStrategy(HystrixConcurrencyStrategy delegate) {
            this.delegate = delegate;
        }

        //  Scheduled 会创建  Worker  由 worker 来调用 schedule() 方法 就会进入到这个方法
        @Override
        public <T> Callable<T> wrapCallable(Callable<T> callable) {
            // 可以配置自己的封装逻辑
            Callable<T> wrappedCallable;
            if (this.delegate != null) {
                wrappedCallable = this.delegate.wrapCallable(callable);
            }
            else {
                wrappedCallable = callable;
            }
            return new JlContextCallable(wrappedCallable);
        }

    }

    public class JlContextCallable implements Callable {

        private Callable target;

        private AuthUser authUser;

        private FeignContextHolder.Feignmonitor feignmonitor;


        public JlContextCallable(Callable target) {
            this.target = target;
            this.authUser = AuthUserContext.get();
            this.feignmonitor = FeignContextHolder.get();
        }

        // 封装为 任务 由线程调用 共享内存来传递 父子线程 来传递参数
        @Override
        public Object call() throws Exception {
            try {
                // 设置进 子线程 变量
                AuthUserContext.set(authUser);
                FeignContextHolder.set(feignmonitor);
                return target.call();
            } finally {
                AuthUserContext.clean();
            }
        }
    }


    /**
     *   属性配置器
     */
    public  class JlDynamicHystrixPropertiesStrategy extends HystrixPropertiesStrategy {

        private HystrixPropertiesStrategy delegate;

        private CustomerHystrixCommandProperties customerHystrixCommandProperties;

        public JlDynamicHystrixPropertiesStrategy(
                CustomerHystrixCommandProperties customerHystrixCommandProperties,HystrixPropertiesStrategy delegate) {
            this.customerHystrixCommandProperties = customerHystrixCommandProperties;
            this.delegate = delegate;
        }


        // 获取到 CommandProperties
        @Override
        public HystrixCommandProperties getCommandProperties(HystrixCommandKey commandKey,
                                                             HystrixCommandProperties.Setter builder) {

            String name = commandKey.name();
            // commandKey 是 HyStrixGatewayFilter 调用的话
            if(name.startsWith(HYSTRIX_GATEWAY_COMMANDKEY)) {
                builder.withExecutionTimeoutEnabled(customerHystrixCommandProperties.isTimeout_enable());
                builder.withExecutionTimeoutInMilliseconds(30000);
                // 配置默认
                HystrixCommandProperties.ExecutionIsolationStrategy executionIsolationStrategy = HystrixCommandProperties.ExecutionIsolationStrategy.THREAD;
                if("SEMAPHORE".equalsIgnoreCase(customerHystrixCommandProperties.getExecution_strategy())) {
                    executionIsolationStrategy = HystrixCommandProperties.ExecutionIsolationStrategy.SEMAPHORE;
                }
                // 改变 隔离策略为 线程隔离 -- 默认 Setter 是 SEMAPHORE
                builder.withExecutionIsolationStrategy(executionIsolationStrategy);
            }else {
                builder.withExecutionTimeoutEnabled(customerHystrixCommandProperties.isTimeout_enable());
                builder.withExecutionTimeoutInMilliseconds(customerHystrixCommandProperties.getTimeout_mills());
                clientCommandKeyTimeoutProperties(name,builder);
            }
            return delegate.getCommandProperties(commandKey, builder);
        }

        /**
         * 设置客户端指定接口的超时设置
         * @param clientCommandKeyName  接口名（例：IUserClient#test(String,String)）
         * @param builder               HystrixCommandProperties
         */
        private void clientCommandKeyTimeoutProperties(String clientCommandKeyName,HystrixCommandProperties.Setter builder){
            Map<String, Integer> map = customerHystrixCommandProperties.getClientCommandKeyTimeout();
            for (Map.Entry<String, Integer> entry : map.entrySet()) {
                String name = entry.getKey();
                if (clientCommandKeyName.equals(name)){
                    builder.withExecutionTimeoutInMilliseconds(entry.getValue());
                    return;
                }
            }
        }

    }

}
