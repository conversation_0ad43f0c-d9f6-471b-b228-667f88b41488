package com.dinglite.feign.config.global;


import com.dinglite.common.global.WebFluxResult;
import com.dinglite.common.threadlocal.FeignContextHolder;
import com.dinglite.common.utils.CommonUtils;
import com.dinglite.feign.config.execption.BaseFeignException;
import com.fasterxml.jackson.databind.ObjectMapper;
import feign.Response;
import feign.codec.ErrorDecoder;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Collection;
import java.util.LinkedList;

/**
 * 错误日志解析器
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@Slf4j
public class JlErrorDecoder extends ErrorDecoder.Default {

    ObjectMapper objectMapper;

    public JlErrorDecoder(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    @Override
    public Exception decode(String methodKey, Response response) {
        Collection<String> collection = response.headers().get(FeignContextHolder.JlFeignRequestHeads.response_system);
        if (collection == null || collection.isEmpty()) {
            return super.decode(methodKey, response);
        }
        LinkedList<String> linkedListAccept = new LinkedList<>(collection);
        if (CommonUtils.isEmpty(linkedListAccept)) {
            return super.decode(methodKey, response);
        }
        String accept = linkedListAccept.get(0);

        int status = response.status();
        if (500 <= status && status <= 800) {
            Collection<String> stringCollection = response.headers().get(FeignContextHolder.JlFeignRequestHeads.response_exceotion_class);
            if (stringCollection == null || stringCollection.isEmpty()) {
                return super.decode(methodKey, response);
            }
            LinkedList<String> linkedList = new LinkedList<>(stringCollection);
            String errorExceptionClass = linkedList.get(0);

            Collection<String> strings = response.headers().get(FeignContextHolder.JlFeignRequestHeads.response_exceotion_message);
            if (strings == null || strings.isEmpty()) {
                return super.decode(methodKey, response);
            }
            LinkedList<String> linkedListTwo = new LinkedList<>(strings);
            String errorExceptionMessage = linkedListTwo.get(0);

            FeignContextHolder.Feignmonitor feignmonitor = FeignContextHolder.get();
            String uid = feignmonitor.getUid();
            String puid = feignmonitor.getPuid();
            String path = feignmonitor.getPath();
            long start = feignmonitor.getStart();
            String method = feignmonitor.getMethod();
            String transfer = feignmonitor.getTransfer();
            log.error("id:{} pid:{} 访问路径为:{} 访问方法为:{} 发起者为:{} 接受方:{} 访问时间为:{}s 异常类:{} 异常堆栈信息:{}",
                    uid, puid, path, method, transfer, accept,
                    (System.currentTimeMillis() - start) / 1000,
                    errorExceptionClass,errorExceptionMessage);
            try {
                WebFluxResult exceptionResult = objectMapper.readValue(response.body().asInputStream(), WebFluxResult.class);
                return new BaseFeignException(status, exceptionResult.getMsg());
            } catch (IOException e) {
                return new BaseFeignException(709, "Feign 错误解码器,json反序列化异常");
            }
        } else {
            return super.decode(methodKey, response);
        }
    }
}
