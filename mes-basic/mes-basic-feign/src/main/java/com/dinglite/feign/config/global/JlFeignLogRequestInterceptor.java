package com.dinglite.feign.config.global;


import com.dinglite.common.threadlocal.FeignContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.core.env.Environment;


public class JlFeignLogRequestInterceptor implements RequestInterceptor {

    private String name = "spring.application.name";

    private Environment environment;


    public JlFeignLogRequestInterceptor(Environment environment) {
        this.environment = environment;
    }

    // 配置请求头时间 和打印日志
    @Override
    public void apply(RequestTemplate template) {
        String puid = "";
        FeignContextHolder.Feignmonitor feignmonitor1 = FeignContextHolder.get();
        if(feignmonitor1 != null) {
            puid = feignmonitor1.getUid();
        }
        //调用方 Application
        String value = environment.getProperty(name);
        String url = template.url();
        String method = template.method();
        String uid = FeignContextHolder.JlFeignRequestHeads.genericUUID(value);
        // 往 Request 添加了请求头
        template.header(FeignContextHolder.JlFeignRequestHeads.contextID,uid);
        // 启动时间
        long startTime = System.currentTimeMillis();
        // 线程一路下去
        FeignContextHolder.Feignmonitor feignmonitor =
                new  FeignContextHolder.Feignmonitor(value,url,method,uid,puid,startTime,0,null);

        FeignContextHolder.set(feignmonitor);
    }





}