package org.springframework.cloud.openfeign;

/**
 * jlFeignClient规范
 *
 * <AUTHOR>
 * @date 2022/03/17
 */
public class JlFeignClientSpecification extends  FeignClientSpecification {

    private String name;

    private Class<?>[] configuration;

    public JlFeignClientSpecification(String name, Class<?>[] configuration) {
        super(name, configuration);
        this.name = name;
        this.configuration = configuration;
    }
}
