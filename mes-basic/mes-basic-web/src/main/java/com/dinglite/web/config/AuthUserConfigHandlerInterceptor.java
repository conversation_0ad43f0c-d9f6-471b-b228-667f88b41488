package com.dinglite.web.config;


import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.constant.auth.AuthConstant;
import com.dinglite.common.global.AuthUser;
import com.dinglite.common.threadlocal.AuthUserContext;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 身份验证用户拦截器配置处理程序
 *
 * <AUTHOR>
 * @date 2023/08/17
 */
@Slf4j
public class AuthUserConfigHandlerInterceptor implements HandlerInterceptor {


    @Override
    public boolean preHandle(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler) {
        if (handler instanceof HandlerMethod) {
            HandlerMethod method = (HandlerMethod) handler;
            if (method.hasMethodAnnotation(LoadPayload.class)
                    || AnnotatedElementUtils.hasAnnotation(method.getBeanType(), LoadPayload.class)) {
                // 获取从网关或者feign调用带过来的 认证数据
                String payloadSource = request.getHeader(AuthConstant.REQUEST_HEAD_AUTH);
                if (StringUtils.isEmpty(payloadSource)) {
                    log.warn("========没有配置认证信息,请检查是否是测试环境====," +
                            "定位原因是Web的AuthUserConfigHandlerInterceptor,找不到从Gateway或者Feign带过来的认证信息");
                }
                if (payloadSource != null) {
                    AuthUserContext.set(AuthUser.deserialize(payloadSource));
                }
            }
        }
        return true;
    }

    @Override
    public void afterCompletion(@NonNull HttpServletRequest request, @NonNull HttpServletResponse response, @NonNull Object handler, Exception ex) {
        AuthUserContext.clean();
    }
}
