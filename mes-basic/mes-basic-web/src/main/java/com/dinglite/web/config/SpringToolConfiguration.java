package com.dinglite.web.config;

import com.dinglite.web.utils.SpringUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @date 2022/03/18
 */
@Configuration
@Order(Ordered.HIGHEST_PRECEDENCE)
public class SpringToolConfiguration {

    /**
     * Spring上下文缓存
     *
     * @return {@link SpringUtils}
     */
    @Bean
    public SpringUtils springUtil() {
        return new SpringUtils();
    }
}
