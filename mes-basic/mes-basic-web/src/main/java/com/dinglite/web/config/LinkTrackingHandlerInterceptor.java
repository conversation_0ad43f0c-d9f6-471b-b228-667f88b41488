package com.dinglite.web.config;


import com.dinglite.common.threadlocal.FeignContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 链接跟踪处理程序拦截器
 * 链路追踪 --> Feign  --> 在响应之后就不能  在往 response 里面写数据了
 *
 * <AUTHOR>
 * @date 2023/08/17
 */
@Slf4j
public class LinkTrackingHandlerInterceptor implements HandlerInterceptor {

    private String name = "spring.application.name";

    Environment environment;

    public LinkTrackingHandlerInterceptor(Environment environment) {
        this.environment = environment;
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        // 需要获取到 Feign 链路调用设置进来的值
        String header = request.getHeader(FeignContextHolder.JlFeignRequestHeads.contextID);
        if (header != null) {
            //初始化一个新的 FeignContextHolder
            FeignContextHolder.setUid(header);
            response.setHeader(FeignContextHolder.JlFeignRequestHeads.contextID, header);
            response.setHeader(FeignContextHolder.JlFeignRequestHeads.response_system, environment.getProperty(name, "UNKOWNS"));
        }
        return true;
    }


    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        String header = request.getHeader(FeignContextHolder.JlFeignRequestHeads.contextID);
        if (header != null) {
            FeignContextHolder.remove();
        }
    }
}
