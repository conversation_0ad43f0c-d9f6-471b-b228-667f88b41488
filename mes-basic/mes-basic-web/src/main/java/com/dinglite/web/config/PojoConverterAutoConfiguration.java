package com.dinglite.web.config;


import com.dinglite.web.utils.ConverterUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.ConversionService;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 */
@Configuration
public class PojoConverterAutoConfiguration {

    @Autowired
    ConversionService conversionService;


    @PostConstruct
    public void init() {
        ConverterUtils.setConversionService(conversionService);
    }

}
