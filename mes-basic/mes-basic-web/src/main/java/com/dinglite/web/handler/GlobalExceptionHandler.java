package com.dinglite.web.handler;


import com.dinglite.common.exception.BusinessException;
import com.dinglite.common.global.WebFluxResult;
import com.dinglite.common.threadlocal.FeignContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.MissingServletRequestParameterException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Arrays;
import java.util.List;

/**
 * 通用异常处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/16 14:52
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {


    /**
     * 通用异常
     *
     * @param e                   e
     * @param httpServletResponse http servlet响应
     * @param httpServletRequest  http servlet请求
     * @return {@link WebFluxResult}
     */
    @ExceptionHandler(value = Throwable.class)
    public WebFluxResult handleException(Exception e,
                                         HttpServletResponse httpServletResponse,
                                         HttpServletRequest httpServletRequest) {
        String applicationName = httpServletResponse.getHeader(FeignContextHolder.JlFeignRequestHeads.response_system);
        String requestURI = httpServletRequest.getRequestURI();
        String method = httpServletRequest.getMethod();
        e.printStackTrace();
        WebFluxResult webFluxResult;

        log.error("请求路径:{} 请求方式:{},异常类:{},异常信息:{}", requestURI, method, e.getClass().getName(), e.getMessage());

        // 如果是 Feign 调用的话
        if (StringUtils.isNotBlank(httpServletRequest.getHeader(FeignContextHolder.JlFeignRequestHeads.contextID))) {
            httpServletResponse.addHeader(FeignContextHolder.JlFeignRequestHeads.response_exceotion_class, e.getClass().getName());
            httpServletResponse.addHeader(FeignContextHolder.JlFeignRequestHeads.response_exceotion_message, Arrays.toString(e.getStackTrace()));
        }

        // 丢失参数异常
        if (e instanceof MissingServletRequestParameterException) {
            //参数丢失异常
            httpServletResponse.setStatus(601);
            webFluxResult = WebFluxResult.exception("参数丢失异常:" + e.getMessage(), requestURI);
        } else if (e instanceof BusinessException) {
            httpServletResponse.setStatus(602);
            webFluxResult = WebFluxResult.exception("业务异常:" + e.getMessage(), requestURI);
        } else if (e instanceof MethodArgumentNotValidException) {
            httpServletResponse.setStatus(603);
            MethodArgumentNotValidException methodArgumentNotValidException = (MethodArgumentNotValidException) e;
            StringBuilder errorMsg = new StringBuilder();
            List<FieldError> fieldErrors = methodArgumentNotValidException.getBindingResult().getFieldErrors();
            for (FieldError fieldError : fieldErrors) {
                String fieldName = fieldError.getField();
                String msg = fieldError.getDefaultMessage();
                errorMsg.append(fieldName).append(":").append(msg).append(";");
            }
            webFluxResult = WebFluxResult.exception("参数校验异常:" + errorMsg, requestURI);
        } else if (e instanceof BindException) {
            httpServletResponse.setStatus(604);
            webFluxResult = WebFluxResult.exception("参数校验异常:" + e.getMessage(), requestURI);
        } else if (e instanceof IllegalArgumentException) {
            httpServletResponse.setStatus(605);
            webFluxResult = WebFluxResult.exception("非法参数异常:" + e.getMessage(), requestURI);
        } else if (e instanceof HttpRequestMethodNotSupportedException) {
            httpServletResponse.setStatus(606);
            webFluxResult = WebFluxResult.exception("请求方式异常:" + e.getMessage(), requestURI);
        } else if ("org.springframework.dao.DuplicateKeyException".equals(e.getClass().getName())) {
            httpServletResponse.setStatus(607);
            webFluxResult = WebFluxResult.exception("字段重复异常:" + e.getMessage(), requestURI);
        } else if ("com.netflix.hystrix.exception.HystrixRuntimeException".equals(e.getClass().getName())) {
            httpServletResponse.setStatus(608);
            webFluxResult = WebFluxResult.exception("服务调用异常:" + e.getCause().getMessage(), requestURI);
        } else {
            httpServletResponse.setStatus(609);
            webFluxResult = WebFluxResult.exception("服务内部异常:" + e.getMessage(), requestURI);
        }

        //applicationName==null表示feign链路最上层的服务，最上层的服务是面向前端的，http状态码要是200
        if (applicationName == null) {
            httpServletResponse.setStatus(200);
        }
        log.error("服务名为：{} 的http状态码为:{}", applicationName, httpServletResponse.getStatus());
        return webFluxResult;
    }


}
