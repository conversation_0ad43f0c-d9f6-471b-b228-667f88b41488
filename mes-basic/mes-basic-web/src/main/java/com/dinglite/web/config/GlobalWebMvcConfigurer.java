package com.dinglite.web.config;


import com.dinglite.common.threadlocal.ServletContextHolder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 全局web mvc配置
 *
 * <AUTHOR>
 * @date 2023/08/17
 */
@Configuration
public class GlobalWebMvcConfigurer implements WebMvcConfigurer {

    @Autowired
    Environment environment;

    /**
     * 添加拦截器
     *
     * @param registry 注册表
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        registry.addInterceptor(new AuthUserConfigHandlerInterceptor());
        registry.addInterceptor(new LinkTrackingHandlerInterceptor(environment));
        registry.addInterceptor(new GlobalServletResponseInterceptor());
    }


    /**
     * 全球servlet响应Interceptor
     *
     * <AUTHOR>
     * @date 2022/03/18
     */
    public static class GlobalServletResponseInterceptor implements HandlerInterceptor {

        @Override
        public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
            // 内存会泄漏 --必须手动释放
            ServletContextHolder.LOCAL.set(response);
            return true;
        }

        @Override
        public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
            ServletContextHolder.LOCAL.remove();
        }
    }


}
