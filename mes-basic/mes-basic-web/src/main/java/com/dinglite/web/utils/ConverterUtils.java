package com.dinglite.web.utils;

import org.springframework.core.convert.ConversionService;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Pojo 通用转换工具.
 * <p>
 * 配置类型:
 * <hr><blockquote><pre>
 *    {@code @Component}
 *     public class Foo2BarConverter implements Converter<Foo, Bar> {
 *        {@code @Override}
 *         public Bar convert(@NonNull Foo source) {
 *             // 转换操作:
 *             Bar target = new Bar();
 *             target.setContent(source.getContent());
 *             . . .
 *             return target;
 *         }
 *     }
 * </pre></blockquote><hr>
 * <p>
 * 使用类型转换可以通过静态方法{@link ConverterUtils#convert(Object, Class)}:
 * <hr><blockquote><pre>
 *     import static com.basic.web.utils.ConverterUtils.convert;
 *
 *     public class DemoService implements Converter<Foo, Bar> {
 *         public Bar demoMethod(Foo foo) {
 *             return convert(foo, Bar.class);
 *         }
 *     }
 * </pre></blockquote><hr>
 *
 * <AUTHOR> Zhang
 */
public abstract class ConverterUtils {

    private static ConversionService conversionService;


    public static void setConversionService(@NonNull ConversionService conversionService) {
        ConverterUtils.conversionService = conversionService;
    }

    public static <S, T> T convert(@Nullable S source, @NonNull Class<T> targetType) {
        if (source == null) {
            return null;
        }
        if (conversionService.canConvert(source.getClass(), targetType)) {
            return conversionService.convert(source, targetType);
        }
        throw new RuntimeException(
                "No converter found capable of converting from type [" + source.getClass() + "] to type [" + targetType + "]");
    }

    public static <S, T> List<T> convert(@Nullable List<S> sources, @NonNull Class<T> targetType) {
        if (sources == null || sources.isEmpty()) {
            return new ArrayList<>();
        }
        return sources.stream().map(source -> convert(source, targetType)).collect(Collectors.toList());
    }

}
