package com.dinglite.web.utils;

import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.converter.FormHttpMessageConverter;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.converter.ResourceHttpMessageConverter;
import org.springframework.http.converter.StringHttpMessageConverter;
import org.springframework.http.converter.json.MappingJackson2HttpMessageConverter;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * 调用 http 工具类
 */
@Slf4j
public class RestTemplateUtils {

    public static String doGetString(String url, Map<String, String> headers, RestTemplate restTemplate) {
        if (headers != null && headers.size() > 0) {
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.setAll(headers);
            HttpEntity requestEntity = new HttpEntity(null, httpHeaders);
            ResponseEntity<String> resEntity = restTemplate.exchange(url, HttpMethod.GET, requestEntity, String.class);
            return resEntity.getBody();
        } else {
            String forObject = restTemplate.getForObject(url, String.class);
            return forObject;
        }
    }

    // 发送 json 数据
    public static JSONObject jsonDoPost(String url, String body, RestTemplate restTemplate) {
        return jsonDoPost(url, body, new HashMap<>(), restTemplate);
    }

    public static JSONObject jsonDoPost(String url, String body, Map<String, String> headers, RestTemplate restTemplate) {
        return jsonDoPost(url, body, headers, new HashMap<>(), restTemplate);
    }

    // 发送 json 数据
    public static JSONObject jsonDoPost(String url, String body, Map<String, String> headers, Map<String, String> uriVariables, RestTemplate restTemplate) {
        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON);
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        HttpEntity<String> request = new HttpEntity<>(body, httpHeaders);

        ResponseEntity<String> jsonObjectResponseEntity = restTemplate.postForEntity(url, request, String.class, uriVariables);
        HttpStatus httpStatus = jsonObjectResponseEntity.getStatusCode();
        if (httpStatus.isError()) {
            throw new RuntimeException("RestTemplate请求URL：" + url + "返回异常:" + jsonObjectResponseEntity);
        }
        String responseEntityBody = jsonObjectResponseEntity.getBody();
        log.warn("RestTemplate请求URL：" + url + "返回:" + responseEntityBody);
        return JSONObject.parseObject(responseEntityBody);
    }


    // 发送form-data数据
    public static JSONObject formDataDoPost(String url, Map<String, String> formData, Map<String, String> headers, Map<String, String> uriVariables, RestTemplate restTemplate) {
        //对中文格式数据进行处理
        FormHttpMessageConverter fc = new FormHttpMessageConverter();
        StringHttpMessageConverter stringConverter = new StringHttpMessageConverter(StandardCharsets.UTF_8);
        List<HttpMessageConverter<?>> partConverters = new ArrayList<>();
        partConverters.add(stringConverter);
        partConverters.add(new ResourceHttpMessageConverter());
        fc.setPartConverters(partConverters);
        restTemplate.getMessageConverters().addAll(Arrays.asList(fc, new MappingJackson2HttpMessageConverter()));

        ResponseEntity<String> jsonObjectResponseEntity = restTemplate.postForEntity(url, popHeaders(formData, headers), String.class, uriVariables);
        HttpStatus httpStatus = jsonObjectResponseEntity.getStatusCode();
        if (httpStatus.isError()) {
            throw new RuntimeException("RestTemplate请求URL：" + url + "返回异常:" + jsonObjectResponseEntity);
        }
        String responseEntityBody = jsonObjectResponseEntity.getBody();
        log.warn("RestTemplate请求URL：" + url + "返回:" + responseEntityBody);

        return JSONObject.parseObject(responseEntityBody);
    }

    // 发送form-data数据
    public static JSONObject formDataDoPost(String url, Map<String, String> formData, RestTemplate restTemplate) {
        return formDataDoPost(url, formData, new HashMap<>(), new HashMap<>(), restTemplate);
    }

    // 发送form-data数据
    public static JSONObject formDataDoPost(String url, Map<String, String> formData, Map<String, String> headers, RestTemplate restTemplate) {
        return formDataDoPost(url, formData, headers, new HashMap<>(), restTemplate);
    }


    //组装请求体
    private static HttpEntity<MultiValueMap<String, String>> popHeaders(Map<String, String> formData, Map<String, String> headers) {
        HttpHeaders httpHeaders = new HttpHeaders();
        for (Map.Entry<String, String> entry : headers.entrySet()) {
            httpHeaders.add(entry.getKey(), entry.getValue());
        }
        httpHeaders.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> map = new LinkedMultiValueMap<String, String>();
        for (Map.Entry<String, String> entry : formData.entrySet()) {
            map.put(entry.getKey(), Collections.singletonList(entry.getValue()));
        }
        HttpEntity<MultiValueMap<String, String>> entity = new HttpEntity<>(map, httpHeaders);
        return entity;
    }


}
