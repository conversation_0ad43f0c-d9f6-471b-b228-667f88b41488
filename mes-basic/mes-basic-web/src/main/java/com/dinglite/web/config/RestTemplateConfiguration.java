package com.dinglite.web.config;

import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

/**
 * 其他模板配置
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Configuration
public class RestTemplateConfiguration {

    /**
     * 其他模板
     * 全局 RestTemplate
     *
     * @param restTemplateBuilder 其他模板编辑器
     * @return {@link RestTemplate}
     */
    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder restTemplateBuilder) {
        return restTemplateBuilder.build();
    }

}
