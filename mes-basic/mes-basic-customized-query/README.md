# 定制化查询模块

---

一个灵活的，可扩展的通用查询工具。

**对于接口提供方：**

- 适配 **Spring boot**，开箱即用。
- 接口提供方只需要按照**规则**创建 **查询实体类** 即可提供**丰富**的查询功能。
- 基于**面向接口而非实现**的设计原则。接口提供方可**隐藏**接口的**实现细节**，仅暴露**受限制**的查询条件。

**对于接口调用方：**

- 为前端提供**查询语法** (详细见下方)，可灵活对查询结果进行**筛选**。
- 为 **Java 端** 的**接口调用方**提供语义清晰的**条件构建器**，自动生成符合 **查询语法** 规范的 **查询语句**。

# 查询语法

---

## 基于 GET 请求传参

> 详细见: AbstractQuery

**Http Request Params:**

### 语法结构:

```http request
GET https://xxx.com/?  conditions= 字段名1#条件1=参数1  &  conditions= 字段名2#条件2=参数2
```

### 示例:

```http request
GET https://xxx.com/query?conditions=name#likeRight="aaa"&conditions=time#ge="2021-01-01"&conditions=time#orderBy=true
```

## 基于 POST 请求传参 (老版本)

> 详细见: AbstractRecoverableQuery

**Http Request Body:**

### 语法结构:

```json5
{
  "condition": {
    "字段1_条件1": "参数1",
    "字段2_条件2": "参数2"
  },
  "orderBy": {
    // 正序 true, 倒序 false
    "排序字段1": true
  }
}
```

### 示例:

```json
{
  "condition": {
    "name_likeRight": "aaa",
    "time_ge": "2021-01-01"
  },
  "orderBy": {
    "time": true
  }
}
```

# 设计与实现

---

> 模块化设计，各组件间均可插拔，方便灵活扩展。

## 处理过程

![](./1.resources/img/1-process.png)

### 示例1

![](./1.resources/img/2-example.png)

> 在此设计中，对 **查询条件** 做了一层抽象 ---- **QueryCondition**。

即，通过 **QueryConditionDeserializer** 对 **查询语句** 进行解析，并统一返回 **QueryCondition**。

**因此：**

### 如果你希望对 "查询语法" 进行扩展:

- 通过自定义 **QueryConditionDeserializer** 的实现，即可自定义 **查询语法**。

### 如果你希望对 "查询条件" 进行定制化处理:

- 通过对 **QueryCondition** 进行二次解析，即可构建最终的查询条件。 (见下方 **对 Mybatis-Plus 的支持**)

## 对 Mybatis-Plus 的支持

![](./1.resources/img/3-mybatisplus.png)

目前实现了对 **Mybatis-Plus** 的支持。即实现了将 **QueryCondition** 转换为 **QueryWrapper** 的解析器。

> 详细见: MybatisPlusQuerier
