package com.dinglite.customizedquery.support.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dinglite.customizedquery.core.AbstractQuery;
import com.dinglite.customizedquery.core.Queryable;
import com.dinglite.customizedquery.exception.ConditionParseException;
import com.dinglite.customizedquery.exception.CustomizedQueryException;
import com.dinglite.customizedquery.serializer.QueryConditionDeserializer;
import com.dinglite.customizedquery.serializer.QueryConditionSerializerContext;
import com.dinglite.customizedquery.support.condition.QueryCondition;
import com.dinglite.customizedquery.utils.QueryDefinition;
import com.dinglite.customizedquery.utils.TableDefinition;
import com.dinglite.customizedquery.utils.TableFieldInfo;

import javax.annotation.Nonnull;
import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Mybatis Plus 查询器
 *
 * <AUTHOR> Zhang
 */
public class MybatisPlusQuerier {

    /**
     * 缓存 查询实体类的字段
     */
    private final Map<Class<? extends AbstractQuery<?>>, QueryDefinition> queryFieldCache;

    /**
     * 缓存 数据库表实体类
     */
    private final Map<Class<?>, TableDefinition> tableDefinitionCache;

    public MybatisPlusQuerier() {
        queryFieldCache = new ConcurrentHashMap<>(256);
        tableDefinitionCache = new ConcurrentHashMap<>(256);
    }

    public <T, Q extends AbstractQuery<Q>> QueryWrapper<T> toWrapper(
            @Nonnull Queryable<Q> query, @Nonnull Class<Q> queryClass, @Nonnull Class<T> tableClass) {
        return toWrapper(new QueryWrapper<>(), query, queryClass, tableClass, null);
    }

    public <T, Q extends AbstractQuery<Q>> QueryWrapper<T> toWrapper(
            @Nonnull Queryable<Q> query, @Nonnull Class<Q> queryClass, @Nonnull Class<T> tableClass, String nickName) {
        return toWrapper(new QueryWrapper<>(), query, queryClass, tableClass, nickName);
    }

    public <T, Q extends AbstractQuery<Q>> QueryWrapper<T> toWrapper(
            @Nonnull QueryWrapper<T> wrapper, @Nonnull Queryable<Q> query,
            @Nonnull Class<Q> queryClass, @Nonnull Class<T> tableClass, String nickName) {

        List<String> conditions = query.getConditions();
        if (conditions == null || conditions.isEmpty()) {
            return wrapper;
        }

        QueryWrapperAdapter<T> adapter = createQueryWrapperAdapter(wrapper);
        QueryConditionDeserializer deserializer = QueryConditionSerializerContext.getDeserializer();

        try {
            // Parsing the expression in the conditions
            for (String expression : conditions) {
                QueryCondition condition = deserializer.deserialize(expression);

                // Convert QueryFiledName to TableFiledName And make sure they're legal
                String queryFieldName = condition.getFieldName();
                String tableFieldName = convertQueryFieldToTableField(queryFieldName, queryClass, tableClass);
                if (!org.apache.commons.lang3.StringUtils.isEmpty(nickName)) {
                    tableFieldName = nickName + "." + tableFieldName;
                }
                condition.setFieldName(tableFieldName);

                adapter.addCondition(condition);
            }
        } catch (ConditionParseException e) {
            throw new CustomizedQueryException(e);
        }

        return adapter.getWrapper();
    }

    protected <T> QueryWrapperAdapter<T> createQueryWrapperAdapter(QueryWrapper<T> queryWrapper) {
        return new DefaultQueryWrapperAdapter<>(queryWrapper);
    }

    /**
     * TODO: 可优化
     * 将查询实体字段转换为数据库字段名
     * <p>将查询实体的字段按照规则转换为与之相对应的数据库表字段
     *
     * <p>转换规则:<br>
     * 如果是 join 字段, 则使用 "表名.字段名"<br>
     * 如果不是 join 字段, 则直接使用 "字段名"
     *
     * @param queryFiledName 查询实体字段名
     * @param entityClass    数据层实体类
     * @return 转换后的数据库字段名
     * @see TableFieldInfo
     */
    @Nonnull
    private String convertQueryFieldToTableField(
            String queryFiledName, Class<? extends AbstractQuery<?>> queryClass, Class<?> entityClass) {

        // 验证 queryFiledName 是否合法
        validateQueryFieldName(queryClass, queryFiledName);

        if (!StringUtils.isCamel(queryFiledName)) {
            throw new IllegalArgumentException("The field name " + queryFiledName + " is must camel");
        }

        TableDefinition tableDefinition = tableDefinitionCache.get(entityClass);
        if (tableDefinition == null) {
            tableDefinition = tableDefinitionCache.computeIfAbsent(entityClass, TableDefinition::new);
        }

        TableFieldInfo fieldInfo = tableDefinition.getFieldInfo(queryFiledName);
        if (fieldInfo == null) {
            throw new IllegalArgumentException("Not found field name " + queryFiledName + " in " + entityClass);
        }

        if (fieldInfo.isJoin()) {
            return fieldInfo.getTableName() + "." + fieldInfo.getFieldName();
        }
        return fieldInfo.getFieldName();
    }

    /**
     * 校验查询字段的合法性
     *
     * @param queryClass 查询实体类
     * @param fieldName  查询字段名
     * @return 查询字段名
     */
    @Nonnull
    @SuppressWarnings("UnusedReturnValue")
    protected String validateQueryFieldName(Class<? extends AbstractQuery<?>> queryClass, String fieldName) {
        QueryDefinition definition = queryFieldCache.get(queryClass);
        if (definition == null) {
            definition = queryFieldCache.computeIfAbsent(queryClass, QueryDefinition::new);
        }

        Field field = definition.getField(fieldName);
        if (field != null) {
            return field.getName();
        }
        throw new CustomizedQueryException("Not found field name " + fieldName + " in class: " + queryClass.getName());
    }

}
