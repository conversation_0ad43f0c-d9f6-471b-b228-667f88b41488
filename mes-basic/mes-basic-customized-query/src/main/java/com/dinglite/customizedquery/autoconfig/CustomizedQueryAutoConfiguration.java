package com.dinglite.customizedquery.autoconfig;


import com.dinglite.customizedquery.serializer.DefaultQueryConditionDeserializer;
import com.dinglite.customizedquery.serializer.DefaultQueryConditionSerializer;
import com.dinglite.customizedquery.serializer.QueryConditionSerializerContext;
import com.dinglite.customizedquery.support.mybatisplus.MybatisPlusQuerier;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 自定义查询配置
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Configuration
public class CustomizedQueryAutoConfiguration {

    public CustomizedQueryAutoConfiguration(ObjectMapper objectMapper) {
        // 初始化 QueryConditionSerializerContext
        DefaultQueryConditionSerializer serializer = new DefaultQueryConditionSerializer(objectMapper);
        DefaultQueryConditionDeserializer deserializer = new DefaultQueryConditionDeserializer(objectMapper);
        QueryConditionSerializerContext.initialize(serializer, deserializer);
    }

    @Bean
    @ConditionalOnClass(name = "com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration")
    public MybatisPlusQuerier mybatisPlusQuerier() {
        return new MybatisPlusQuerier();
    }

}
