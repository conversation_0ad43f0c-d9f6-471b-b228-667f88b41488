package com.dinglite.customizedquery.serializer;


import com.dinglite.customizedquery.exception.CustomizedQueryException;

/**
 * 查询条件序列化器上下文
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public class QueryConditionSerializerContext {

    private static QueryConditionSerializer serializer;

    private static QueryConditionDeserializer deserializer;

    public static void initialize(QueryConditionSerializer serializer,
                                  QueryConditionDeserializer deserializer) {
        QueryConditionSerializerContext.serializer = serializer;
        QueryConditionSerializerContext.deserializer = deserializer;
    }

    public static QueryConditionSerializer getSerializer() {
        if (serializer == null) {
            throw new CustomizedQueryException("Uninitialized the QueryConditionSerializerContext");
        }
        return serializer;
    }

    public static QueryConditionDeserializer getDeserializer() {
        if (deserializer == null) {
            throw new CustomizedQueryException("Uninitialized the QueryConditionSerializerContext");
        }
        return deserializer;
    }

}
