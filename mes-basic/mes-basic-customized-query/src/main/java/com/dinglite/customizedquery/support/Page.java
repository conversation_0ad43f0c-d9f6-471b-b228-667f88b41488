package com.dinglite.customizedquery.support;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;

/**
 * 页面
 * 分页包装
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Page<T> {

    private List<T> records = Collections.emptyList();

    /**
     * 总页数
     */
    private Long total;

    /**
     * 当前分页总页数
     */
    private Long pages;

}
