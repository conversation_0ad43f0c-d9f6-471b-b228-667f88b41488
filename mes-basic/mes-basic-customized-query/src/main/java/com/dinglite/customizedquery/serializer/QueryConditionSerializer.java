package com.dinglite.customizedquery.serializer;


import com.dinglite.customizedquery.support.condition.QueryCondition;

import javax.annotation.Nonnull;

/**
 * 查询条件序列化器
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public interface QueryConditionSerializer {

    /**
     * 序列化
     *
     * @param condition 条件
     * @return {@link String}
     */
    String serialize(@Nonnull QueryCondition condition);

}
