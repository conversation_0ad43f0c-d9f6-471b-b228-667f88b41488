package com.dinglite.customizedquery.support;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 分页查询
 * 分页查询包装器
 * （本类已过期，请使用 {@link com.dinglite.customizedquery.core.PaginationQuery}
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Data
@Deprecated
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class PaginationQuery<T extends AbstractRecoverableQuery> extends Pagination {

    private T query;

    public PaginationQuery(int current, int size, T query) {
        super(current, size);
        this.query = query;
    }

    public static <T extends AbstractRecoverableQuery> PaginationQuery<T> query(int current, int size, T query) {
        return new PaginationQuery<>(current, size, query);
    }

    public static <T extends AbstractRecoverableQuery> PaginationQuery<T> query(T query) {
        return new PaginationQuery<>(1, 10, query);
    }

}
