package com.dinglite.customizedquery.support.condition;


import com.dinglite.customizedquery.exception.CustomizedQueryException;
import lombok.Getter;

import javax.annotation.Nonnull;

/**
 * 状态标志
 * 条件的符号
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public enum ConditionSymbol {
    /**
     * 等于
     */
    EQ("eq", false),
    /**
     * 不等于
     */
    NE("ne", false),
    /**
     * 大于
     */
    GT("gt", false),
    /**
     * 大于等于
     */
    GE("ge", false),
    /**
     * 小于
     */
    LT("lt", false),
    /**
     * 小于等于
     */
    LE("le", false),
    /**
     * 批量等于
     */
    IN("in", true),
    /**
     * 区间
     */
    BETWEEN("between", true),
    /**
     * 右模糊匹配
     */
    LIKE_RIGHT("likeRight", false),
    /**
     * 全模糊匹配
     */
    LIKE("like", false),
    /**
     * 左模糊匹配
     */
    LIKE_LEFT("likeLeft", false),
    /**
     * 排序
     */
    ORDER_BY("orderBy", false);

    @Getter
    private final String symbol;

    @Getter
    private final boolean multiValues;

    @Nonnull
    public static ConditionSymbol symbolOf(String symbol) {
        for (ConditionSymbol value : ConditionSymbol.values()) {
            if (value.getSymbol().equals(symbol)) {
                return value;
            }
        }
        throw new CustomizedQueryException("Illegal symbol: " + symbol);
    }

    ConditionSymbol(String symbol, boolean multiValues) {
        this.symbol = symbol;
        this.multiValues = multiValues;
    }
}
