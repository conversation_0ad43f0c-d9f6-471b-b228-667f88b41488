package com.dinglite.customizedquery.utils;

import lombok.Getter;
import org.springframework.util.ReflectionUtils;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.Map;

/**
 * 表定义
 * 数据库表定义信息
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public class TableDefinition {

    @Getter private final Class<?> clazz;

    private final Map<String, TableFieldInfo> fieldInfoMap;

    public TableDefinition(@Nonnull Class<?> clazz) {
        this.clazz = clazz;
        fieldInfoMap = new HashMap<>();

        ReflectionUtils.doWithFields(clazz, field ->
                fieldInfoMap.put(field.getName(), new TableFieldInfo(field)));
    }

    @Nullable
    public TableFieldInfo getFieldInfo(@Nonnull String fieldName) {
        return fieldInfoMap.get(fieldName);
    }

}
