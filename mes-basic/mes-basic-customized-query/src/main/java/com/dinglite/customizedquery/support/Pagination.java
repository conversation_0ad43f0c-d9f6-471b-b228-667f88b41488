package com.dinglite.customizedquery.support;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 分页
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class Pagination {

    @Min(1)
    @NotNull
    private Integer current;

    @Min(1)
    @NotNull
    private Integer size;

    public static Pagination query(int current, int size) {
        return new Pagination(current, size);
    }

    @JsonIgnore
    public Integer getOffset() {
        return getSize() * (getCurrent() - 1);
    }

    @JsonIgnore
    public Integer getLimit() {
        return getSize();
    }

}
