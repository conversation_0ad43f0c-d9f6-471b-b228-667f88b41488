package com.dinglite.customizedquery.support.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dinglite.customizedquery.support.condition.QueryCondition;

import javax.annotation.Nonnull;

/**
 * 查询包装器适配器
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public interface QueryWrapperAdapter<T> {

    /**
     * 得到包装
     *
     * @return {@link QueryWrapper}<{@link T}>
     */
    QueryWrapper<T> getWrapper();

    /**
     * 添加条件
     *
     * @param condition 条件
     */
    void addCondition(@Nonnull QueryCondition condition);

}
