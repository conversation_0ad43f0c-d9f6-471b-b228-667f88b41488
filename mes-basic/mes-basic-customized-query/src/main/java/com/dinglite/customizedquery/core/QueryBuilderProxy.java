package com.dinglite.customizedquery.core;


import com.dinglite.customizedquery.exception.CustomizedQueryException;
import com.dinglite.customizedquery.serializer.QueryConditionSerializer;
import com.dinglite.customizedquery.serializer.QueryConditionSerializerContext;
import com.dinglite.customizedquery.support.Range;
import com.dinglite.customizedquery.support.condition.ConditionSymbol;
import com.dinglite.customizedquery.support.condition.QueryCondition;
import com.dinglite.customizedquery.utils.StringUtils;
import org.springframework.cglib.proxy.Enhancer;
import org.springframework.cglib.proxy.MethodInterceptor;
import org.springframework.cglib.proxy.MethodProxy;

import javax.annotation.Nonnull;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 查询构建者代理
 *
 * <AUTHOR> Zhang
 */
class QueryBuilderProxy implements MethodInterceptor {

    /**
     * 构建者类型的后缀名
     */
    private static final String BUILDER_TYPE_TAIL = "Builder";

    /**
     * 由 {@link lombok.Builder} 生成的构建者对象中的 build() 方法的方法名
     */
    private static final String BUILD_METHOD = "build";

    /**
     * 自定义的可排序字段命名前缀
     */
    private static final String ORDER_BY_METHOD_PREFIX = "orderBy";

    /**
     * 查询条件序列化器
     */
    private final QueryConditionSerializer serializer;

    /**
     * 被代理对象, 实体类构建者  ({@link lombok.Builder} 生成的)
     */
    private final Object builder;

    /**
     * 记录的查询条件
     */
    private final List<QueryCondition> conditions;

    // Static

    /**
     * 创建 Builder ({@link lombok.Builder} 生成的实体类构建者) 代理
     *
     * @param builder {@link lombok.Builder} 生成的实体类构建者
     * @param <T>     构建者类型
     * @return 代理后的构建者
     */
    @SuppressWarnings("unchecked")
    static <T> T create(T builder) {
        builderIsLegal(builder);

        Enhancer eh = new Enhancer();
        eh.setSuperclass(builder.getClass());
        eh.setCallback(new QueryBuilderProxy(builder));

        return (T) eh.create();
    }

    /**
     * 查询构建者代理的构造器
     *
     * @param builder {@link lombok.Builder} 生成的实体类构建者
     */
    QueryBuilderProxy(Object builder) {
        this.serializer = QueryConditionSerializerContext.getSerializer();
        this.builder = builder;
        this.conditions = new ArrayList<>();
    }

    /**
     * 校验是否为 {@link lombok.Builder} 生成的构建者对象
     */
    private static void builderIsLegal(Object builder) {
        // 仅允许名为 XxxBuilder 的对象
        if (!builder.getClass().getName().endsWith(BUILDER_TYPE_TAIL)) {
            throw new CustomizedQueryException("Invalid builder: " + builder);
        }
    }


    // Member

    /**
     * 查询构建者代理拦截器
     */
    @Override
    public Object intercept(Object proxy, Method method, Object[] args, MethodProxy methodProxy) throws Throwable {
        String fieldName = method.getName();

        // build(): 装载查询条件, 并直接调用 obj 自身的 build()
        if (isBuildMethod(method)) {
            return buildQuery(method, args);
        }

        QueryCondition condition;

        // Boolean orderByXxx(): 指定对字段的排序方向
        if (isOrderByMethod(method)) {
            String realFieldName = StringUtils.firstToLowerCase(fieldName.substring(7));
            condition = createOrderByCondition(realFieldName, (Boolean) args[0]);
        }
        // Range<Xxx> xxx(): 指定对字段的范围查询规则
        else if (isRangeMethod(method)) {
            condition = createRangeCondition(fieldName, (Range<?>) args[0]);
        }
        // Xxx xxx(): 指定对字段的等值查询规则
        else {
            condition = createDefaultCondition(fieldName, args[0]);
        }

        addCondition(condition);
        return proxy;
    }

    /**
     * 构建查询实体
     * <p>代理 {@link lombok.Builder} 的 build() 方法, 进行查询条件的组装
     */
    @Nonnull
    protected AbstractQuery<?> buildQuery(Method method, Object[] args) throws Throwable {
        AbstractQuery<?> query = (AbstractQuery<?>) method.invoke(builder, args);
        List<String> conditions = this.conditions.stream().map(serializer::serialize).collect(Collectors.toList());
        query.setConditions(conditions);
        return query;
    }

    /**
     * 创建 OrderBy 查询条件
     *
     * @param fieldName 字段名
     * @param isAsc     是否升序
     * @return 查询条件
     */
    @Nonnull
    protected QueryCondition createOrderByCondition(String fieldName, Boolean isAsc) {
        return new QueryCondition(fieldName, ConditionSymbol.ORDER_BY, isAsc == null || isAsc);
    }

    /**
     * 创建 Range 查询条件
     *
     * @param fieldName 字段名
     * @param range     范围查询条件
     * @return 查询条件
     */
    @Nonnull
    protected QueryCondition createRangeCondition(String fieldName, Range<?> range) {
        if (range == null) {
            throw new CustomizedQueryException("The argument \"range\" cannot be null");
        }
        Range.Compare compare = range.getCompare();
        if (compare.isMulValues()) {
            return new QueryCondition(fieldName, ConditionSymbol.symbolOf(compare.getAlias()), range.getValues());
        }
        return new QueryCondition(fieldName, ConditionSymbol.symbolOf(compare.getAlias()), range.getVal());
    }

    /**
     * 创建默认查询条件 (等值查询)
     *
     * @param fieldName 字段名
     * @param value     值
     * @return 查询条件
     */
    @Nonnull
    protected QueryCondition createDefaultCondition(String fieldName, Object value) {
        return new QueryCondition(fieldName, ConditionSymbol.EQ, value);
    }

    /**
     * 是否为 {@link lombok.Builder} 生成的 build 方法
     */
    protected boolean isBuildMethod(Method method) {
        return method.getName().startsWith(BUILD_METHOD);
    }

    /**
     * 是否为指定排序方向的方法
     */
    protected boolean isOrderByMethod(Method method) {
        return method.getName().startsWith(ORDER_BY_METHOD_PREFIX);
    }

    /**
     * 是否为指定范围查询的方法
     */
    protected boolean isRangeMethod(Method method) {
        return method.getParameterTypes()[0].isAssignableFrom(Range.class);
    }

    /**
     * 追加查询规则
     *
     * @param condition 查询规则
     */
    protected void addCondition(QueryCondition condition) {
        conditions.add(condition);
    }

}
