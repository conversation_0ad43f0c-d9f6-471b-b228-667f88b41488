package com.dinglite.customizedquery.support.mybatisplus;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.dinglite.customizedquery.exception.CustomizedQueryException;
import com.dinglite.customizedquery.support.condition.ConditionSymbol;
import com.dinglite.customizedquery.support.condition.QueryCondition;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.util.Collection;
import java.util.List;

/**
 * QueryWrapper 适配器
 *
 * <AUTHOR> Zhang
 */
final class DefaultQueryWrapperAdapter<T> implements QueryWrapperAdapter<T> {

    private final @Getter QueryWrapper<T> wrapper;

    public DefaultQueryWrapperAdapter(QueryWrapper<T> queryWrapper) {
        wrapper = queryWrapper;
    }

    @Override
    public void addCondition(@Nonnull QueryCondition condition) {
        String fieldName = condition.getFieldName();
        ConditionSymbol symbol = condition.getSymbol();
        Object value = condition.getValue();

        // Skip if value is null
        if (value == null) {
            return;
        }

        switch (symbol) {
            case EQ:
                wrapper.eq(fieldName, value);
                return;
            case NE:
                wrapper.ne(fieldName, value);
                return;
            case GT:
                wrapper.gt(fieldName, value);
                return;
            case GE:
                wrapper.ge(fieldName, value);
                return;
            case LT:
                wrapper.lt(fieldName, value);
                return;
            case LE:
                wrapper.le(fieldName, value);
                return;
            case IN: {
                Collection<?> coll = (Collection<?>) value;
                if (coll.isEmpty()) {
                    throw new CustomizedQueryException("The value must not be a empty list, but was: " + value);
                }
                wrapper.in(fieldName, coll);
                return;
            }
            case BETWEEN: {
                List<?> values;
                if (!(value instanceof List) || (values = (List<?>) value).size() != 2) {
                    throw new CustomizedQueryException("The value must not be a list other than length 2");
                }
                wrapper.between(fieldName, values.get(0), values.get(1));
                return;
            }
            case LIKE_RIGHT: {
                if (value.toString().contains("%")) {
                    throw new CustomizedQueryException("The value must not contain %");
                }
                wrapper.likeRight(fieldName, value);
                return;
            }
            case LIKE: {
                if (value.toString().contains("%")) {
                    throw new CustomizedQueryException("The value must not contain %");
                }
                wrapper.like(fieldName, value);
                return;
            }
            case LIKE_LEFT: {
                if (value.toString().contains("%")) {
                    throw new CustomizedQueryException("The value must not contain %");
                }
                wrapper.likeLeft(fieldName, value);
                return;
            }
            case ORDER_BY: {
                if ((boolean) value) {
                    wrapper.orderByAsc(fieldName);
                } else {
                    wrapper.orderByDesc(fieldName);
                }
                return;
            }
            default:
                throw new CustomizedQueryException("Unsupported symbol: " + symbol);
        }
    }

}
