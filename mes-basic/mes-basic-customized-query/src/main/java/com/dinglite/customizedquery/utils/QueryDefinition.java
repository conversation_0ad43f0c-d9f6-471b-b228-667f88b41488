package com.dinglite.customizedquery.utils;

import lombok.Getter;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;

/**
 * 查询定义
 * 查询实体类定义信息
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public class QueryDefinition {

    @Getter private final Class<?> clazz;

    private final Map<String, Field> fieldMap;

    public QueryDefinition(@Nonnull Class<?> clazz) {
        this.clazz = clazz;
        Field[] fields = clazz.getDeclaredFields();
        fieldMap = new HashMap<>((int) (fields.length / .75));
        for (Field field : fields) {
            fieldMap.put(field.getName(), field);
        }
    }

    @Nullable
    public Field getField(String fieldName) {
        return fieldMap.get(fieldName);
    }

}
