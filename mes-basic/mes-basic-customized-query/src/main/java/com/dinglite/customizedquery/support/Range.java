package com.dinglite.customizedquery.support;

import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Arrays;
import java.util.Collection;

/**
 * 范围
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Getter
@NoArgsConstructor
public class Range<T> {

    private Compare compare;

    /**
     * 单参数
     * <p>因为绝大多数情况下都仅有一个参数 (优化单参数情况)
     */
    private T val;

    /**
     * 多参数
     */
    private Collection<T> values;

    public Range(Compare compare, T val) {
        this.compare = compare;
        this.val = val;
    }

    public Range(Compare compare, Collection<T> values) {
        this.compare = compare;
        this.values = values;
    }

    public static <T> Range<T> eq(T val) {
        return new Range<>(Compare.EQ, val);
    }

    public static <T> Range<T> ne(T val) {
        return new Range<>(Compare.NE, val);
    }

    public static <T> Range<T> gt(T val) {
        return new Range<>(Compare.GT, val);
    }

    public static <T> Range<T> ge(T val) {
        return new Range<>(Compare.GE, val);
    }

    public static <T> Range<T> lt(T val) {
        return new Range<>(Compare.LT, val);
    }

    public static <T> Range<T> le(T val) {
        return new Range<>(Compare.LE, val);
    }

    @SafeVarargs
    public static <T> Range<T> in(T... values) {
        return in(Arrays.asList(values));
    }

    public static <T> Range<T> in(Collection<T> values) {
        return new Range<>(Compare.IN, values);
    }

    public static <T> Range<T> between(T val1, T val2) {
        return new Range<>(Compare.BETWEEN, Arrays.asList(val1, val2));
    }

    public static <T> Range<T> likeRight(T val) {
        return new Range<>(Compare.LIKE_RIGHT, val);
    }

    public enum Compare {
        /**
         * 等于
         */
        EQ("eq", false),
        /**
         * 不等于
         */
        NE("ne", false),
        /**
         * 大于
         */
        GT("gt", false),
        /**
         * 大于等于
         */
        GE("ge", false),
        /**
         * 小于
         */
        LT("lt", false),
        /**
         * 小于等于
         */
        LE("le", false),
        /**
         * 传入collection
         */
        IN("in", true),
        /**
         * 区间
         */
        BETWEEN("between", true),
        /**
         * 右模糊匹配
         */
        LIKE_RIGHT("likeRight", false);

        @Getter private final String alias;

        @Getter private final boolean mulValues;

        Compare(String alias, boolean mulValues) {
            this.alias = alias;
            this.mulValues = mulValues;
        }
    }

}
