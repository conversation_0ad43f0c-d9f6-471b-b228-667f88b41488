package com.dinglite.customizedquery.support;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dinglite.customizedquery.utils.Asserts;
import com.dinglite.customizedquery.utils.QueryDefinition;
import com.dinglite.customizedquery.utils.TableDefinition;
import com.dinglite.customizedquery.utils.TableFieldInfo;
import lombok.SneakyThrows;

import javax.annotation.Nonnull;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * mybatis +恢复
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Deprecated
public class MybatisPlusRecover {

    /**
     * 缓存 请求实体类
     */
    public static Map<Class<? extends AbstractRecoverableQuery>, QueryDefinition> queryDefinitionCache =
            new ConcurrentHashMap<>(256);

    /**
     * 缓存 数据库表实体类
     */
    public static Map<Class<?>, TableDefinition> tableDefinitionCache = new ConcurrentHashMap<>(256);

    /**
     * getCondition 的反射方法
     *
     * @see AbstractRecoverableQuery#getCondition()
     */
    private static final Method GET_CONDITION_METHOD;

    /**
     * getOrderBy 的反射方法
     *
     * @see AbstractRecoverableQuery#getOrderBy()
     */
    private static final Method GET_ORDER_BY_METHOD;

    static {
        try {
            GET_CONDITION_METHOD = AbstractRecoverableQuery.class.getDeclaredMethod("getCondition");
            GET_ORDER_BY_METHOD = AbstractRecoverableQuery.class.getDeclaredMethod("getOrderBy");
            /*
             * 优化:
             * 覆盖访问权限, 使得调用 invoke() 时可直接跳过 JVM 对访问权限的校验
             *
             * @see AccessibleObject#override
             */
            GET_CONDITION_METHOD.setAccessible(true);
            GET_ORDER_BY_METHOD.setAccessible(true);
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 将查询实体转换为 QueryWrapper
     */
    @SneakyThrows
    public static <S extends AbstractRecoverableQuery, T> QueryWrapper<T> toWrapper(
            @Nonnull S recoverable, @Nonnull Class<T> targetClass) {

        Asserts.notNull("toWrapper#recoverable", recoverable);
        Asserts.notNull("toWrapper#targetClass", targetClass);
        return doToWrapper(new QueryWrapper<>(), recoverable, targetClass);
    }

    @SuppressWarnings("unchecked")
    private static <S extends AbstractRecoverableQuery, T> QueryWrapper<T> doToWrapper(
            QueryWrapper<T> wrapper, S recoverable, Class<T> targetClass)
            throws InvocationTargetException, IllegalAccessException {

        // Compose "where"
        Map<String, Object> conditionMap = (Map<String, Object>) GET_CONDITION_METHOD.invoke(recoverable);
        if (conditionMap != null) {
            for (Map.Entry<String, Object> entry : conditionMap.entrySet()) {
                String fieldName;
                String conditionName = entry.getKey();
                Object value = entry.getValue();

                if (value == null) {
                    continue;
                }

                // Is range
                if (conditionName.contains("_")) {
                    int conditionNameLen = conditionName.length();
                    // eq
                    if (conditionName.endsWith("_" + Range.Compare.EQ.getAlias())) {
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.eq(fieldNameConvert(fieldName, targetClass), value);
                    }
                    // ne
                    else if (conditionName.endsWith("_" + Range.Compare.NE.getAlias())) {
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.ne(fieldNameConvert(fieldName, targetClass), value);
                    }
                    // gt
                    else if (conditionName.endsWith("_" + Range.Compare.GT.getAlias())) {
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.gt(fieldNameConvert(fieldName, targetClass), value);
                    }
                    // ge
                    else if (conditionName.endsWith("_" + Range.Compare.GE.getAlias())) {
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.ge(fieldNameConvert(fieldName, targetClass), value);
                    }
                    // lt
                    else if (conditionName.endsWith("_" + Range.Compare.LT.getAlias())) {
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.lt(fieldNameConvert(fieldName, targetClass), value);
                    }
                    // le
                    else if (conditionName.endsWith("_" + Range.Compare.LE.getAlias())) {
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.le(fieldNameConvert(fieldName, targetClass), value);
                    }
                    // in
                    else if (conditionName.endsWith("_" + Range.Compare.IN.getAlias())) {
                        if (!(value instanceof Collection)) {
                            throw new IllegalArgumentException();
                        }
                        Collection<?> values = (Collection<?>) value;
                        if (values.isEmpty()) {
                            continue;
                        }
                        fieldName = conditionName.substring(0, conditionNameLen - 3);
                        assertQueryField(recoverable, fieldName);
                        wrapper.in(fieldNameConvert(fieldName, targetClass), values);
                    }
                    // between
                    else if (conditionName.endsWith("_" + Range.Compare.BETWEEN.getAlias())) {
                        List<?> values;
                        if (!(value instanceof List) || (values = (List<?>) value).size() < 2) {
                            throw new IllegalArgumentException();
                        }
                        fieldName = conditionName.substring(0, conditionNameLen - 8);
                        assertQueryField(recoverable, fieldName);
                        wrapper.between(fieldNameConvert(fieldName, targetClass), values.get(0), values.get(1));
                    }
                    // like right
                    else if (conditionName.endsWith("_" + Range.Compare.LIKE_RIGHT.getAlias())) {
                        if (value.toString().contains("%")) {
                            throw new IllegalArgumentException("The % is not allowed here");
                        }
                        fieldName = conditionName.substring(0, conditionNameLen - 10);
                        assertQueryField(recoverable, fieldName);
                        wrapper.likeRight(fieldNameConvert(fieldName, targetClass), value);
                    }
                }
                // Normal condition
                else {
                    fieldName = conditionName;
                    assertQueryField(recoverable, fieldName);
                    wrapper.eq(fieldNameConvert(fieldName, targetClass), value);
                }
            }
        }

        // Compose "order by"
        Map<String, Boolean> orderByMap = (Map<String, Boolean>) GET_ORDER_BY_METHOD.invoke(recoverable);
        if (orderByMap != null) {
            for (Map.Entry<String, Boolean> entry : orderByMap.entrySet()) {
                String fieldName = entry.getKey();
                assertQueryField(recoverable, fieldName);
                Boolean isAscending = entry.getValue();
                if (isAscending == null || isAscending) {
                    wrapper.orderByAsc(fieldNameConvert(fieldName, targetClass));
                } else {
                    wrapper.orderByDesc(fieldNameConvert(fieldName, targetClass));
                }
            }
        }

        return wrapper;
    }

    /**
     * 将查询实体字段转换为数据库字段名
     * <p>将查询实体的字段按照规则转换为与之相对应的数据库表字段
     *
     * <p>转换规则:<br>
     * 如果是 join 字段, 则使用 "表名.字段名"<br>
     * 如果不是 join 字段, 则直接使用 "字段名"
     *
     * @param fieldName   查询实体字段名
     * @param targetClass 数据层实体类
     * @return 转换后的数据库字段名
     * @see TableFieldInfo
     */
    private static String fieldNameConvert(String fieldName, Class<?> targetClass) {
        if (!StringUtils.isCamel(fieldName)) {
            throw new IllegalArgumentException("The field name " + fieldName + " is not camel");
        }

        TableDefinition tableDefinition = tableDefinitionCache.get(targetClass);
        if (tableDefinition == null) {
            tableDefinition = tableDefinitionCache.computeIfAbsent(targetClass, TableDefinition::new);
        }

        TableFieldInfo fieldInfo = tableDefinition.getFieldInfo(fieldName);
        if (fieldInfo == null) {
            throw new IllegalArgumentException("Not found field name " + fieldName + " in " + targetClass);
        }

        if (fieldInfo.isJoin()) {
            return fieldInfo.getTableName() + "." + fieldInfo.getFieldName();
        }
        return fieldInfo.getFieldName();
    }

    private static <S extends AbstractRecoverableQuery> void assertQueryField(S query, String fieldName) {
        QueryDefinition queryDefinition = queryDefinitionCache.get(query.getClass());
        if (queryDefinition == null) {
            queryDefinition = queryDefinitionCache.computeIfAbsent(query.getClass(), QueryDefinition::new);
        }

        if (queryDefinition.getField(fieldName) == null) {
            throw new IllegalArgumentException("Not found field name " + fieldName);
        }
    }

}
