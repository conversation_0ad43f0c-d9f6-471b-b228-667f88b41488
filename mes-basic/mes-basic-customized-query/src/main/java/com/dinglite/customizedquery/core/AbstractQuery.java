package com.dinglite.customizedquery.core;


import com.dinglite.customizedquery.support.AbstractRecoverableQuery;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * 抽象查询
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@SuppressWarnings("deprecation")
public abstract class AbstractQuery<T extends AbstractQuery<?>>
        // 兼容 AbstractRecoverQuery, 迭代结束后将删除
        extends AbstractRecoverableQuery implements Queryable<T> {

    /**
     * 查询条件列表
     */
    @Getter
    @Setter
    @JsonProperty
    private List<String> conditions;

    /**
     * 为 Builder ({@link lombok.Builder} 生成的) 创建代理.
     * 以支持记录调用过程, 用于组装查询条件
     *
     * @param builder {@link lombok.Builder} 生成的实体类构建者
     * @param <T>     构建者的类型
     * @return 代理后的构建者
     */
    protected static <T> T builderProxy(T builder) {
        return QueryBuilderProxy.create(builder);
    }

}
