package com.dinglite.customizedquery.serializer;


import com.dinglite.customizedquery.exception.CustomizedQueryException;
import com.dinglite.customizedquery.support.condition.QueryCondition;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;

import javax.annotation.Nonnull;
import java.time.temporal.Temporal;
import java.util.Collection;

/**
 * <AUTHOR> Zhang
 */
@RequiredArgsConstructor
public class DefaultQueryConditionSerializer implements QueryConditionSerializer {

    private final ObjectMapper objectMapper;

    @Override
    public String serialize(@Nonnull QueryCondition condition) {
        if (condition.getValue() == null) {
            return serializeNullValue(condition);
        }
        if (condition.getSymbol().isMultiValues()) {
            return serializeMultiValues(condition);
        }
        return serializeSingleValue(condition);
    }

    protected String serializeMultiValues(@Nonnull QueryCondition condition) {
        Object value = condition.getValue();
        if (value.getClass().isArray() || value instanceof Collection) {
            return pack(condition.getFieldName(), condition.getSymbol().getSymbol(), value);
        }
        throw new CustomizedQueryException("The condition \"" + condition.getSymbol().name() +
                "\" type must is Collection or Array, but was: " + value.getClass().getName());
    }

    protected String serializeSingleValue(@Nonnull QueryCondition condition) {
        Object value = condition.getValue();
        if (!(value.getClass().isArray() || value instanceof Collection)) {
            return pack(condition.getFieldName(), condition.getSymbol().getSymbol(), value);
        }
        throw new CustomizedQueryException("The condition \"" + condition.getSymbol().name() +
                "\" type must not is Collection or Array, but was: " + value.getClass().getName());
    }

    protected String serializeNullValue(@Nonnull QueryCondition condition) {
        return condition.getFieldName() + '#' + condition.getSymbol().getSymbol() + "=null";
    }

    protected String pack(String fieldName, String symbol, Object value) {
        if (value == null) {
            return fieldName + '#' + symbol + "=null";
        }
        String realValue;

        // TODO: 此处兼容没有开启 JSR 310 的 Client, 暂未找到较好的解决方案
        if (value instanceof Temporal) {
            realValue = '"' + value.toString() + '"';
        }
        // 使用 JSON 对 value 进行序列化
        else {
            try {
                realValue = objectMapper.writeValueAsString(value);
            } catch (JsonProcessingException e) {
                throw new CustomizedQueryException(e);
            }
        }
        return fieldName + '#' + symbol + '=' + realValue;
    }

}
