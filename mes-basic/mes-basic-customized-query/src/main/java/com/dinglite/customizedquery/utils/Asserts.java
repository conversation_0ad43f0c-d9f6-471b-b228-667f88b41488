package com.dinglite.customizedquery.utils;

import org.springframework.util.Assert;

/**
 * 断言
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public class Asserts {

    public static void notNull(Object object, String message) {
        Assert.notNull(object, message);
    }

    public static void notNull(String argName, Object object) {
        Assert.notNull(object, "[<PERSON><PERSON><PERSON> failed] - this argument " + argName + " is required; it must not be null");
    }

    public static void isTrue(boolean expression) {
        Assert.isTrue(expression, "[<PERSON><PERSON><PERSON> failed] - this expression must be true");
    }

}
