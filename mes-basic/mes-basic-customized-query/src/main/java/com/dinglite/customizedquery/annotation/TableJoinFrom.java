package com.dinglite.customizedquery.annotation;

import java.lang.annotation.*;

/**
 * 表连接
 * 用于当查询结果需要通过多表 Join 时, 指定字段定义的表
 *
 * <AUTHOR>
 * @date 2022/03/18
 * @see com.dinglite.customizedquery.utils.TableFieldInfo
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface TableJoinFrom {

    /**
     * 指定包含该字段的表(或表别名), 使用此参数优先级高于 {@link #tableClass()}
     */
    String table() default "";

    /**
     * 指定包含该字段的表信息的实体类
     *
     * <p>表名优先级:<br>
     * 1. 实体类上的 {@link com.baomidou.mybatisplus.annotation.TableName#value()}<br>
     * 2. 实体类的类名 toLowerCase()
     */
    Class<?> tableClass() default Void.class;

    /**
     * 指定表字段名, 优先级高于 {@link com.baomidou.mybatisplus.annotation.TableField}
     *
     * @see com.baomidou.mybatisplus.annotation.TableField
     */
    String field() default "";

}
