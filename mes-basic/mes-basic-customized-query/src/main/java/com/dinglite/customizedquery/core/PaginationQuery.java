package com.dinglite.customizedquery.core;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * 分页查询
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Data
public class PaginationQuery<T extends AbstractQuery<T>> implements Queryable<T> {

    @Min(1)
    @NotNull
    private Integer current;

    @Min(1)
    @NotNull
    private Integer size;

    /**
     * 查询条件列表
     */
    private List<String> conditions = new ArrayList<>();

    public static <T extends AbstractQuery<T>> PaginationQuery<T> query(int current, int size, T query) {
        return new PaginationQuery<>(current, size, query);
    }

    public static <T extends AbstractQuery<T>> PaginationQuery<T> query(T query) {
        return new PaginationQuery<>(1, 10, query);
    }

    public PaginationQuery(@Min(1) int current, @Min(1) int size, T query) {
        this.current = current;
        this.size = size;
        this.conditions = query.getConditions();
    }

    @JsonIgnore
    public Integer getOffset() {
        return getSize() * (getCurrent() - 1);
    }

    @JsonIgnore
    public Integer getLimit() {
        return getSize();
    }

    public PaginationQuery(){
        current = 1;
        size = 10;
    }

}
