package com.dinglite.customizedquery.serializer;


import com.dinglite.customizedquery.exception.ConditionParseException;
import com.dinglite.customizedquery.support.condition.ConditionSymbol;
import com.dinglite.customizedquery.support.condition.QueryCondition;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import javax.annotation.Nonnull;
import javax.annotation.Nullable;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> Zhang
 */
@RequiredArgsConstructor
public class DefaultQueryConditionDeserializer implements QueryConditionDeserializer {

    private final ObjectMapper objectMapper;

    @Override
    public QueryCondition deserialize(@Nonnull String src) throws ConditionParseException {
        // 表达式必须包含 '='
        int eqIdx = src.indexOf('=');
        if (eqIdx == -1) {
            throw new ConditionParseException("Invalid expression: " + src);
        }

        // 在 '=' 的位置进行切割, 分别为 prefix 与 value
        Prefix prefix = deserializePrefix(src.substring(0, eqIdx));
        Object value = deserializeValue(prefix, src.substring(eqIdx + 1));

        return new QueryCondition(prefix.getFieldName(), prefix.getSymbol(), value);
    }

    @Nonnull
    protected Prefix deserializePrefix(@Nonnull String prefix) {
        String fieldName;
        ConditionSymbol symbol;
        int wellIdx = prefix.indexOf('#');

        /*
         * 默认行为, 当 prefix 没有 '#', 则当作 EQ
         * e.g:
         * prefix = "filedName"
         */
        if (wellIdx == -1) {
            fieldName = prefix;
            symbol = ConditionSymbol.EQ;
        }
        /*
         * 当 prefix 出现 '#'. 则 '#' 左边的为 fieldName, 右边的为 symbol
         * e.g:
         * prefix = 'fieldName#symbol'
         */
        else {
            fieldName = prefix.substring(0, wellIdx);
            symbol = ConditionSymbol.symbolOf(prefix.substring(wellIdx + 1));
        }

        return new Prefix(fieldName, symbol);
    }

    @Nullable
    protected Object deserializeValue(@Nonnull Prefix prefix, @Nonnull String value) throws ConditionParseException {
        if (prefix.getSymbol().isMultiValues()) {
            return deserializeMultiValues(value);
        }
        return deserializeSingleValue(value);
    }

    @Nonnull
    private List<?> deserializeMultiValues(@Nonnull String value) throws ConditionParseException {
        if ("null".equals(value)) {
            throw new ConditionParseException("The value type must is Array, but was: null");
        }
        try {
            return objectMapper.readValue(value, List.class);
        } catch (IOException e) {
            throw new ConditionParseException(e);
        }
    }

    @Nullable
    private Object deserializeSingleValue(@Nonnull String value) throws ConditionParseException {
        Object obj;
        try {
            obj = objectMapper.readValue(value, Object.class);
        } catch (IOException e) {
            throw new ConditionParseException(e);
        }
        if (obj instanceof List || obj instanceof Map) {
            throw new ConditionParseException("The value type must is not is Array or Object, but was: " + value);
        }
        return obj;
    }

    @Getter
    @AllArgsConstructor
    static class Prefix {

        private final String fieldName;

        private final ConditionSymbol symbol;

    }

}
