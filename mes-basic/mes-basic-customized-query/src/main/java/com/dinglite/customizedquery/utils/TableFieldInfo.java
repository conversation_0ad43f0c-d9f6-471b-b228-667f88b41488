package com.dinglite.customizedquery.utils;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.dinglite.customizedquery.annotation.TableJoinFrom;
import lombok.Getter;

import javax.annotation.Nonnull;
import java.lang.reflect.Field;

/**
 * 表字段信息
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
@Getter
public class TableFieldInfo {

    private final Field field;

    private final String tableName;

    private final String fieldName;

    private final boolean isJoin;

    public TableFieldInfo(@Nonnull Field field) {
        this.field = field;
        tableName = getTableName(field);
        fieldName = getFieldName(field);
        isJoin = field.getAnnotation(TableJoinFrom.class) != null;
    }

    /**
     * 获取 tableName
     *
     * <p>获取规则:<br>
     * 1. 字段上的注解 {@link TableJoinFrom#table()} 的值<br>
     * 2. 字段上的注解 {@link TableJoinFrom} 指向的类 上的 {@link TableName#value()}<br>
     * 3. 字段上的注解 {@link TableJoinFrom} 指向的类 的类名 toLowerCase()<br>
     * 4. 字段的声明类 上的 {@link TableName#value()}<br>
     * 5. 字段的声明类 的类名 toLowerCase()
     *
     * @param field 字段
     * @return tableName
     */
    @Nonnull
    private static String getTableName(@Nonnull Field field) {
        Class<?> tableClass = null;

        TableJoinFrom tableJoinFrom = field.getAnnotation(TableJoinFrom.class);
        if (tableJoinFrom != null) {
            if (!StringUtils.isBlank(tableJoinFrom.table())) {
                return tableJoinFrom.table();
            }
            if (tableJoinFrom.tableClass() != Void.class) {
                tableClass = tableJoinFrom.tableClass();
            }
        }

        // Default
        if (tableClass == null) {
            tableClass = field.getDeclaringClass();
        }

        TableName tableNameAnnotation = tableClass.getAnnotation(TableName.class);
        if (tableNameAnnotation != null && !StringUtils.isBlank(tableNameAnnotation.value())) {
            return tableNameAnnotation.value();
        }

        return StringUtils.camelToUnderline(tableClass.getSimpleName());
    }

    /**
     * 获取 fieldName
     *
     * <p>获取规则:<br>
     * 1. 字段上的注解 {@link TableJoinFrom#field()}<br>
     * 2. 字段上的注解 {@link TableField#value()}<br>
     * 3. 字段的字段名 toLowerCase()
     *
     * @param field 字段
     * @return fieldName
     */
    @Nonnull
    private static String getFieldName(@Nonnull Field field) {
        TableJoinFrom tableJoinFrom = field.getAnnotation(TableJoinFrom.class);
        if (tableJoinFrom != null && !StringUtils.isBlank(tableJoinFrom.field())) {
            return tableJoinFrom.field();
        }

        TableField tableField = field.getAnnotation(TableField.class);
        if (tableField != null && !StringUtils.isBlank(tableField.value())) {
            return tableField.value();
        }

        return StringUtils.camelToUnderline(field.getName());
    }

}
