package com.dinglite.common.configuration;

/**
 * 配置属性前缀康士坦茨湖
 *
 * <AUTHOR>
 * @date 2022/03/17
 */
public class ConfigurationPropertyPrefixConstance {

    // 自定义配置文件 所有的 属性名前缀
    public final static String PRE_PROPERTY_NAME = "mes.property";

    // 所属系统
    public final static String GATEWAY = ".gateway";

    // 所属 功能
    public final static String FUNCTION_SECURITY = ".security";

    // 所属 功能
    public final static String FUNCTION_HYSTRIX = ".strix";


    // 网关忽略请求的 url
    public static final String IGNORE_PATH = PRE_PROPERTY_NAME+GATEWAY + FUNCTION_SECURITY + ".ignore.path";

    //Hystrix配置项
    public static final String HYSTRIX_PATH = PRE_PROPERTY_NAME + FUNCTION_HYSTRIX + ".command";

    public static final String HYSTRIX_GATEWAY_PATH = PRE_PROPERTY_NAME + GATEWAY + FUNCTION_HYSTRIX + ".command";

}
