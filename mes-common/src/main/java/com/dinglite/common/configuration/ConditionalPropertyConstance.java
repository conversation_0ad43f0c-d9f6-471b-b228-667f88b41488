package com.dinglite.common.configuration;

/**
 * 用来配置 类是否装载
 *
 * <AUTHOR>
 * @date 2022/03/24
 */
public class ConditionalPropertyConstance {

    // 所有属性的前缀
    public final static String PRE_NAME = "jl";

    // 是否自动配置 网关
    public final static String AUTO_CUSTOMER_GATEWAY = "gateway.enable";


    // 是否自动配置  安全过滤器
    public final static String AUTO_CUSTOMER_SECURITY = "security.enable";


    // 属性自动配置
    public final static String PRE_NAME_PROPERTIES = "jl";

}
