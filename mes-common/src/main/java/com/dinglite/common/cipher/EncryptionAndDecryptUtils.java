package com.dinglite.common.cipher;


import com.dinglite.common.constant.auth.AuthConstant;
import lombok.SneakyThrows;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;

/**
 * 加密 和 解密 工具类
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
public class EncryptionAndDecryptUtils {

    /**
     * base64 解密
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String base64decode(String value) {
        byte[] decode = Base64.getDecoder().decode(value);
        return  new String(decode);
    }

    /**
     * base64 加密
     *
     * @param value 价值
     * @return {@link String}
     */
    public static String base64Encode(String value) {
        byte[] decode = Base64.getEncoder().encode(value.getBytes());
        return  new String(decode);
    }


    /**
     * 加盐操作
     *
     * @param password 密码
     * @param userName 用户名
     * @return {@link String}
     */
    public static String addSalt(String password, String userName) {
        return AuthConstant.PASSWORD_SALT + userName + password;
    }


    /**
     * 利用java原生的摘要实现SHA256加密
     *
     * @param str 加密后的报文
     * @return {@link String}
     */
    @SneakyThrows
    public static String string2SHA256(String str) {

        byte[] bytes = str.getBytes(StandardCharsets.UTF_8);

        return byte2SHA256(bytes);
    }


    /**
     * 利用java原生的摘要实现SHA256加密
     *
     * @param bytes 字节数组
     * @return {@link String}
     */
    @SneakyThrows
    public static String byte2SHA256(byte[] bytes) {
        MessageDigest messageDigest;
        String encodeStr = "";
        try {
            messageDigest = MessageDigest.getInstance("SHA-256");
            messageDigest.update(bytes);
            encodeStr = byte2Hex(messageDigest.digest());
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        return encodeStr;
    }

    /**
     * 将byte转为16进制
     *
     * @param bytes 字节
     * @return {@link String}
     */
    private static String byte2Hex(byte[] bytes) {
        StringBuilder stringBuffer = new StringBuilder();
        String temp;
        for (byte aByte : bytes) {
            temp = Integer.toHexString(aByte & 0xFF);
            if (temp.length() == 1) {
                //1得到一位的进行补0操作
                stringBuffer.append("0");
            }
            stringBuffer.append(temp);
        }
        return stringBuffer.toString();
    }

    public static void main(String[] args) {
        System.out.println(base64Encode("123456"));
    }

}
