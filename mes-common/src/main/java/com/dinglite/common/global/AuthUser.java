package com.dinglite.common.global;


import com.dinglite.common.cipher.EncryptionAndDecryptUtils;
import com.dinglite.common.constant.auth.AuthConstant;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * 服务间传递的`Payload`实体
 *
 * <AUTHOR>
 * <AUTHOR>
 */
@Data
@Builder(access = AccessLevel.PRIVATE)
@NoArgsConstructor
@AllArgsConstructor
public class AuthUser {

    /**
     * 客户id
     */
    private String cid;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 角色id
     */
    private String rids;

    /**
     * 系统id
     */
    private String sid;

    /**
     * 用户名
     */
    private String username;


    /**
     * 将当前{@link AuthUser}对象序列化成`base64`.
     *
     * @return base64
     */
    public static String serialize(AuthUser authUser) {
        return EncryptionAndDecryptUtils.base64Encode(
                StringUtils.join(Arrays.asList(
                        authUser.cid,
                        authUser.uid,
                        authUser.rids,
                        authUser.sid,
                        authUser.username
                ), AuthConstant.SESSION_STROAGE_SPLITTER));
    }

    /**
     * 将`base64`串反序列化回{@link AuthUser}对象.
     *
     * @param value base64
     * @return {@link AuthUser}
     */
    public static AuthUser deserialize(String value) {

        AuthUser authUser = null;
        try {
            String[] sourceDataArray = EncryptionAndDecryptUtils.base64decode(value)
                    .split(AuthConstant.SESSION_STROAGE_SPLITTER);
            authUser = AuthUser.builder()
                    .cid(sourceDataArray[0])
                    .uid(sourceDataArray[1])
                    .rids(sourceDataArray[2])
                    .sid(sourceDataArray[3])
                    .username(sourceDataArray[4])
                    .build();
        } catch (ArrayIndexOutOfBoundsException | NullPointerException e) {
            e.printStackTrace();
        }

        return authUser;
    }

    @Override
    public String toString() {
        return "AuthUser{" +
                "所属客户id='" + cid + '\'' +
                ", 登录用户id='" + uid + '\'' +
                ", 登录用户名='" + username + '\'' +
                ", 用户拥有角色='" + rids + '\'' +
                ", 正在访问系统='" + sid + '\'' +
                '}';
    }

    public static void main(String[] args) {
        System.out.println(serialize(new AuthUser("1","2","3","4","hlc")));
    }
}
