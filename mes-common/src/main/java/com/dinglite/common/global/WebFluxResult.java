package com.dinglite.common.global;

import lombok.Data;

import java.io.Serializable;

@Data
public class WebFluxResult<T> implements Serializable {

    private int code;

    private String path;

    private String msg;

    private T data;



    public WebFluxResult(int code, String msg, T data, String path) {
        this.code = code;
        this.msg = msg;
        this.data = data;
        this.path = path;
    }


    public static WebFluxResult success(Object data) {
        return success(data, null);
    }

    public static WebFluxResult success(Object data, String path) {
        return create(200,"请求成功",data,path);
    }

    public static WebFluxResult exception(String exceptionMessage) {
        return exception(exceptionMessage,null);
    }

    public static WebFluxResult exception(String exceptionMessage,String path) {
        return create(500,exceptionMessage,null,path);
    }

    public static WebFluxResult fail(String fialMessage) {
        return fail(fialMessage, null);
    }

    public static WebFluxResult fail(String fialMessage, String path) {
        return create(400, fialMessage, null, path);
    }


    private static WebFluxResult create(int code, String message, Object data, String path) {
        return new WebFluxResult(code, message, data, path);
    }

    public static WebFluxResult invalidSession(String fialMessage, String path) {
        return create(600, fialMessage, null, path);
    }

}
