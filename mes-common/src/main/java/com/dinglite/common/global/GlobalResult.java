package com.dinglite.common.global;


import com.dinglite.common.exception.HttpStatus;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 全球的结果
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
@Data
@Accessors(chain = true)
@NoArgsConstructor
public class GlobalResult<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    private int code;

    private T data;

    private String msg;

    public GlobalResult(int code, T data, String msg) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    public boolean isOk() {
        return this.code == HttpStatus.SUCCESS;
    }

    public static <T> GlobalResult<T> success() {
        return new GlobalResult(HttpStatus.SUCCESS, null, "成功");
    }

    public static <T> GlobalResult<T> successMsg(String msg) {
        return new GlobalResult(HttpStatus.SUCCESS, null, msg);
    }

    public static <T> GlobalResult<T> failed(String msg) {
        return new GlobalResult(HttpStatus.BAD_REQUEST, null, msg);
    }

    public static <T> GlobalResult<T> failed(int code, String msg) {
        return new GlobalResult(code, null, msg);
    }

    public static <T> GlobalResult<T> exception(Exception e) {
        return new ExceptionResult(HttpStatus.ERROR, null, "API业务异常", e);
    }

    public static <T> GlobalResult<T> failed() {
        return new GlobalResult(HttpStatus.BAD_REQUEST, null, "失败");
    }

    public static <T> GlobalResult<T> success(T data) {
        return new GlobalResult<>(HttpStatus.SUCCESS, data, "成功");
    }

    /**
     * 状态
     *
     * @param flag 国旗
     * @return {@link GlobalResult}<{@link T}>
     */
    public static <T> GlobalResult<T> status(boolean flag) {
        return flag ? success() : failed();
    }

    public static <T> GlobalResult<T> success(T data, String msg) {
        return new GlobalResult<>(HttpStatus.SUCCESS, data, msg);
    }

    public GlobalResult<T> reply(int code, T data, String msg) {
        return new GlobalResult<>(code, data, msg);
    }

}
