package com.dinglite.common.global;

import com.alibaba.fastjson.JSON;
import lombok.NoArgsConstructor;

@NoArgsConstructor
public class ExceptionResult extends GlobalResult {

    private String exceptionString;

    private String exceptonClassName;

    private String eMsg;

    public String errorMsg(){
        return eMsg;
    }

    public String exceptionName(){
        return exceptonClassName;
    }

    public Throwable cause(){
        Throwable throwablec = null;
        try {
            throwablec = (Throwable)JSON.parseObject(exceptionString, Class.forName(exceptonClassName));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return throwablec;
    }

    public void setException(Exception e){
        this.exceptonClassName = e.getClass().getName();
        this.exceptionString = JSON.toJSONString(e);
        this.eMsg = e.getMessage();
    }

    @Override
    public String toString() {
        return super.toString();
    }


    public ExceptionResult(int code, Object data, String msg, Exception e){
        super(code,data,msg);
        this.setException(e);
    }

    public String getExceptionString() {
        return exceptionString;
    }

    public void setExceptionString(String exceptionString) {
        this.exceptionString = exceptionString;
    }

    public String getExceptonClassName() {
        return exceptonClassName;
    }

    public void setExceptonClassName(String exceptonClassName) {
        this.exceptonClassName = exceptonClassName;
    }

    public String geteMsg() {
        return eMsg;
    }

    public void seteMsg(String eMsg) {
        this.eMsg = eMsg;
    }
}


