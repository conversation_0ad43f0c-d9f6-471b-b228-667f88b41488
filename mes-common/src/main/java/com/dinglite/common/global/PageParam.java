package com.dinglite.common.global;

import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.util.Map;

/**
 * 页面参数
 *
 * <AUTHOR>
 * @date 2022/03/15
 */
@Data
@Accessors(chain = true)
public class PageParam {

    @Min(0)
    @NotNull
    private long current = 1L;

    @Range(min = 1, max = 100)
    @NotNull
    private long size = 10L;

    /**
     * 模糊查询关键词
     */
    private String key;

    /**
     * 条件查询
     */
    private Map<String, String> queryParam;

    private String startTime;

    private String endTime;

    /**
     * 排序字段是
     */
    private String orderBy;

    /**
     * sort类型 填asc或desc
     */
    private String sort;

    public boolean isEmpty(){
        boolean empty=true;
        empty = empty&&key==null;
        empty = empty&&queryParam==null;
        empty = empty&&startTime==null;
        empty = empty&&endTime==null;
        empty = empty&&orderBy==null;
        empty = empty&&sort==null;
        return empty;
    }
}
