package com.dinglite.common.constant.auth;

public class SsoConstant {

    public static String LOGIN_SMS_URL = "/login/sms";

    public static String LOGIN_WECHAT_URL = "/login/wechat";

    public static String PHONE_CHECK_USNAME = "phone";

    public static String PHONE_CHECK_CODE = "code";

//    public static String WECHAT_ACCESS_TOKEN = "access_token";
public static String WECHAT_ACCESS_TOKEN = "code";

    public static String SMS_CODE = "SMS:CODE:";

    public static String OS_TYPE = "TYPE";

    public static String AUTH_CACHE = "AUTH:USER:";

    public static String SUPER_ADMIN_ROLE = "jiuling_admin";

    public static String AUTH_SESSION_STORAGE = "AUTH:SESSION:";

    public static int ONLINE_LOGIN_COUNT = 3;

    public static long AUTH_SESSION_STORAGE_TIMEOUT = 36000L;

    public static String SESSION_STROAGE_SPLITTER = "#";

}
