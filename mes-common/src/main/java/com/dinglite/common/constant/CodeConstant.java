package com.dinglite.common.constant;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-9-8 16:44
 */
public class CodeConstant {


    /**
     * 仓库编码前缀
     */
    public static final String STORE_PRE = "CK";
    /**
     * 仓库编码表名
     */
    public static final String STORE_TABLE_NAME = "store";
    /**
     * 仓库编码字段名称
     */
    public static final String STORE_FIELD_NAME = "store_code";
    /**
     * 仓库编码日期格式
     */
    public static final String STORE_PATTERN = "yyMMdd";


    /**
     * 仓位编码前缀
     */
    public static final String STORE_PLACE_PRE = "CW";
    /**
     * 仓位编码表名称
     */
    public static final String STORE_PLACE_TABLE_NAME = "store_place";
    /**
     * 仓位编码字段名称
     */
    public static final String STORE_PLACE_FIELD_NAME = "place_code";
    /**
     * 仓位编码日期格式
     */
    public static final String STORE_PLACE_PATTERN = "yyMMdd";


    /**
     * 物料类型编码前缀
     */
    public static final String MATERIAL_TYPE_PRE = "WLT";
    /**
     * 物料类型编码表名
     */
    public static final String MATERIAL_TYPE_TABLE_NAME = "material_type";
    /**
     * 物料类型编码字段名
     */
    public static final String MATERIAL_TYPE_FIELD_NAME = "material_type_code";
    /**
     * 物料类型编码日期格式
     */
    public static final String MATERIAL_TYPE_PRE_PATTERN = "yyMMdd";


    /**
     * 物料编码前缀
     */
    public static final String MATERIAL_PRE = "WL";
    /**
     * 物料编码表名称
     */
    public static final String MATERIAL_TABLE_NAME = "material";
    /**
     * 物料编码字段名称
     */
    public static final String MATERIAL_FIELD_NAME = "material_code";
    /**
     * 物料编码日期格式
     */
    public static final String MATERIAL_PRE_PATTERN = "yyMMdd";


    /**
     * 客户供应商编码前缀
     */
    public static final String CUSTOMER_SUPPLIER_PRE = "CS";
    /**
     * 客户供应商编码表名称
     */
    public static final String CUSTOMER_SUPPLIER_TABLE_NAME = "customer_supplier";
    /**
     * 客户供应商编码字段名
     */
    public static final String CUSTOMER_SUPPLIER_FIELD_NAME = "code";
    /**
     * 客户供应商编码日期格式
     */
    public static final String CUSTOMER_SUPPLIER_PRE_PATTERN = "yyMMdd";


    /**
     * 入库单编码前缀
     */
    public static final String ENTER_STORAGE_PRE = "RK";
    /**
     * 入库单编码表名
     */
    public static final String ENTER_STORAGE_TABLE_NAME = "enter_storage";
    /**
     * 入库单编码字段名称
     */
    public static final String ENTER_STORAGE_FIELD_NAME = "enter_storage_code";
    /**
     * 入库单编码日期格式
     */
    public static final String ENTER_STORAGE_PRE_PATTERN = "yyMMdd";


    /**
     * 出库单编码前缀
     */
    public static final String OUT_STORAGE_PRE = "OSK";
    /**
     * 出库单编码表名称
     */
    public static final String OUT_STORAGE_TABLE_NAME = "out_storage";
    /**
     * 出库单编码字段名称
     */
    public static final String OUT_STORAGE_FIELD_NAME = "out_storage_code";
    /**
     * 出库单编码日期格式
     */
    public static final String OUT_STORAGE_PRE_PATTERN = "yyMMdd";


    /**
     * 工序编码前缀
     */
    public static final String OPERATION_PRE = "SGX";
    /**
     * 工序编码表名称
     */
    public static final String OPERATION_TABLE_NAME = "operation";
    /**
     * 工序编码字段名
     */
    public static final String OPERATION_FIELD_NAME = "operation_code";
    /**
     * 工序编码日期格式
     */
    public static final String OPERATION_PRE_PATTERN = "yyMMdd";


    /**
     * 路由编码前缀
     */
    public static final String ROUTE_PRE = "LY";
    /**
     * 路由编码表名称
     */
    public static final String ROUTE_TABLE_NAME = "route";
    /**
     * 路由编码字段名
     */
    public static final String ROUTE_FIELD_NAME = "route_code";
    /**
     * 路由编码日期格式
     */
    public static final String ROUTE_PRE_PATTERN = "yyMMdd";


    /**
     * 产线编码前缀
     */
    public static final String PRODUCT_LINE_PRE = "PL";
    /**
     * 产线编码表名
     */
    public static final String PRODUCT_LINE_TABLE_NAME = "product_line";
    /**
     * 产线编码字段名
     */
    public static final String PRODUCT_LINE_FIELD_NAME = "product_line_code";
    /**
     * 产线编码日期格式
     */
    public static final String PRODUCT_LINE_PRE_PATTERN = "yyMMdd";


    /**
     * 设备编码前缀
     */
    public static final String DEVICE_PRE = "SB";
    /**
     * 设备编码表名称
     */
    public static final String DEVICE_TABLE_NAME = "device";
    /**
     * 设备编码字段名
     */
    public static final String DEVICE_FIELD_NAME = "device_code";
    /**
     * 设备编码日期格式
     */
    public static final String DEVICE_PRE_PATTERN = "yyMMdd";


    /**
     * 不良编码前缀
     */
    public static final String BAD_PRE = "BAD";
    /**
     * 不良编码表名
     */
    public static final String BAD_TABLE_NAME = "bad";
    /**
     * 不良编码字段名
     */
    public static final String BAD_FIELD_NAME = "bad_code";
    /**
     * 不良编码日期格式
     */
    public static final String BAD_PRE_PATTERN = "yyMMdd";


    /**
     * 工艺bom编码前缀
     */
    public static final String BOM_PRE = "BOM";
    /**
     * 工艺bom编码表名
     */
    public static final String BOM_TABLE_NAME = "bom";
    /**
     * 工艺bom编码字段名
     */
    public static final String BOM_FIELD_NAME = "bom_code";

    /**
     * 工艺bom编码日期格式
     */
    public static final String BOM_PRE_PATTERN = "yyMMdd";


    /**
     * 生产工单编码前缀
     */
    public static final String PRODUCT_ORDER_PRE = "PO";
    /**
     * 生产工单编码表名
     */
    public static final String PRODUCT_ORDER_TABLE_NAME = "product_order";
    /**
     * 生产工单编码字段名
     */
    public static final String PRODUCT_ORDER_FIELD_NAME = "product_order_code";
    /**
     * 生产工单编码日期格式
     */
    public static final String PRODUCT_ORDER_PRE_PATTERN = "yyMMdd";


    /**
     * 标签箱号前缀
     */
    public static final String TAG_PRE = "CWYJ";
    /**
     * 标签表名称
     */
    public static final String TAG_TABLE_NAME = "tag_detail";
    /**
     * 标签字段名
     */
    public static final String TAG_FIELD_NAME = "box_number";
    /**
     * 标签箱号前缀日期格式
     */
    public static final String TAG_PRE_PATTERN = "yyyyMMdd";

    /**
     * 送检单编码前缀
     */
    public static final String LABORATORY_SEND_PRE = "SJ";
    /**
     * 送检单编码表名
     */
    public static final String LABORATORY_SEND_TABLE_NAME = "laboratory_send";
    /**
     * 送检单编码字段名
     */
    public static final String LABORATORY_SEND_FIELD_NAME = "laboratory_number";


    /**
     * 样品编码前缀
     */
    public static final String SAMPLE_PRE = "YP";
    /**
     * 样品编码表名
     */
    public static final String SAMPLE_TABLE_NAME = "sample";
    /**
     * 样品编码字段名
     */
    public static final String SAMPLE_FIELD_NAME = "sample_number";


    /**
     * 生产工单损耗单编码前缀
     */
    public static final String PRODUCT_ORDER_LOSS_PRE = "LOSS";
    /**
     * 生产工单损耗单编码表名
     */
    public static final String PRODUCT_ORDER_LOSS_TABLE_NAME = "product_order_loss";
    /**
     * 生产工单损耗单编码字段名
     */
    public static final String PRODUCT_ORDER_LOSS_FIELD_NAME = "product_order_loss_code";
    /**
     * 生产工单损耗单编码日期格式
     */
    public static final String PRODUCT_ORDER_LOSS_PRE_PATTERN = "yyyyMMdd";


    /**
     * 样品编码前缀
     */
    public static final String SAMPLE_LOT_PRE = "YP";
    /**
     * 样品编码表名
     */
    public static final String SAMPLE_LOT_TABLE_NAME = "lims_sample_lot";
    /**
     * 样品编码字段名
     */
    public static final String SAMPLE_LOT_FIELD_NAME = "lot_msg";

}
