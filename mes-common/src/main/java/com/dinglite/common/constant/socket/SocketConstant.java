package com.dinglite.common.constant.socket;


/**
 * 套接字不变
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
public interface SocketConstant {

    String DOWNLOAD_RESULT = "socket:download-result";
    String START_RESULT = "socket:start-result";
    String STOP_RESULT = "socket:stop-result";
    String DOWNLOAD = "download";
    String START = "start";
    String STOP = "stop";
    /**
     * 定时查询温箱信息
     */
    String SELECT = "select";
    /**
     * 客户端匹配温箱实验室场地
     */
    String MATCH = "match";
    String REPLY = "reply";

    /**
     * 恒定实验远程启动温箱
     */
    String CONSTANT_START = "constant-start";
    /**
     * 程序实验远程启动温箱
     */
    String PROGRAM_START = "program-start";

    /**
     * 恒定实验远程停止温箱
     */
    String CONSTANT_STOP = "constant-stop";

    /**
     * 程序实验远程停止温箱
     */
    String PROGRAM_STOP = "program-stop";

    /**
     * 查询温箱已注册程序号
     */
    String SELECT_PROGRAM = "select-program";
}
