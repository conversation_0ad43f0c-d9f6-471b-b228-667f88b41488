package com.dinglite.common.constant.auth;


/**
 * auth常数
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public interface AuthConstant {

    // 请求头带的 SESSIONID
    String SESSION_ID = "SESSIONID";

    // SESSION ID 分割符
    String SESSION_STROAGE_SPLITTER = "#";

    // 存放进 Request 中 属性
    String REQUEST_ATTR_USERID = "USERID";

    // 存放进 Request 中 属性
    String REQUEST_ATTR_CLIENTID = "CLIENTID";

    // 存放进 Request 中 属性
    String REQUEST_ATTR_SYSTEM = "SYSTEM_ID";

    // 存放进 Request 中 属性
    String REQUEST_ATTR_RIDS = "RIDS";

    //存放进 Request 中 属性
    String REQUEST_ATTR_USER_NAME = "USER_NAME";

    // 存进 HEAD 中属性 集合
    String REQUEST_HEAD_AUTH = "AUTH_ATTR";


    // redis 中 SESSION  key de前缀
    String AUTH_SESSION_STORAGE = "AUTH:SESSION:";

    // redis 中 user key de前缀
    String AUTH_USER = "AUTH:USER:";

    // session id 过期时间
    long SESSIOND_TIME = 60 * 60;

    String PASSWORD_SALT = "jlw";


    // 微信认证 token
    String WECHAT_TOKEN = "wechat:token:";

    // 京东商城认证 token
    String JD_TOKEN = "JD:token:";


}
