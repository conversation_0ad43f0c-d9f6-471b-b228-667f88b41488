package com.dinglite.common.constant.feign;

/**
 * 复述,api常数
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
public interface RedisApiConstant {

    
    //String FEIGN_REDIS_PACKAGE = "com.base.api.redis";

    String FEIGN_REDISSON_PACKAGE = "com.dinglite.redis.api.service";

    String FEIGN_REDIS_CONTEXT_ID = "mes-redis";

    // 服务前缀
    String SERVICE_PREX = "/api/redis";

    String SERVICE_SELECT = SERVICE_PREX + "/select";

    String SERVICE_SAVE = SERVICE_PREX + "/save";

    String SERVICE_REFERSH = SERVICE_PREX + "/refersh";

    String SERVICE_RANGE = SERVICE_PREX + "/range";

    String SERVICE_LOCK = SERVICE_PREX + "/lock";

    String SERVICE_DELETE = SERVICE_PREX + "/delete";

    String SERVICE_SIZE = SERVICE_PREX + "/sizes";

    String SERVICE_EXIST = SERVICE_PREX + "/exist_value";

    String SERVICE_MEMBER_EXIST = SERVICE_PREX + "/exist_member_value";


    String SERVICE_GET_ID = SERVICE_PREX + "/getId";




}
