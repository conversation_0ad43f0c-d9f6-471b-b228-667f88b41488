package com.dinglite.common.constant;


import com.dinglite.common.global.AuthUser;

/**
 * 通用常量类
 *
 * <AUTHOR>
 * @date 2023/08/29
 */
public interface CommonConstant {
    /**
     * 默认密码
     */
    String DEFAULT_PASSWORD = "123456";


    /**
     * 管理组 租户
     */
    String TENANT_ADMIN = "1";

    /**
     * 管理员客户
     */
    String CLIENT_ADMIN = "1164733399668962220";

    /**
     * 顶级父节点id
     */
    String TOP_PARENT_ID = "0";

    /**
     * 顶级父节点名称
     */
    String TOP_PARENT_NAME = "根节点";

    /**
     * 超级管理角色
     */
    String SUPER_ADMIN_ROLE = "1164733399668962220";

    /**
     * 超级管理角色
     */
    String SUPER_ADMIN_ROLE_ID = "1164733399668962220";

    /**
     * 上传路径(相对路径)
     */
    String UPLOAD_PATH = "/upload";

    /**
     * 项目相对路径
     */
    String CONTEXT_PATH = "/";

    /**
     * 字符串默认分隔符
     */
    String SEPARATOR_CHAR = ",";

    /**
     * 路径分隔符
     */
    String PATH_SEPARATOR_CHAR = "/";

    /**
     * 运营管理系统id
     */
    String ADMIN_SYSTEM_ID = "1";

    /**
     * OA系统id
     */
    String OA_SYSTEM_ID = "2";

    /**
     * ERP系统id
     */
    String ERP_SYSTEM_ID = "3";

    /**
     * 管理员id
     */
    String ADMIN_USER_ID = "1164733399668962220";


    String UPLOAD_CONTEXT_PATH = CONTEXT_PATH + UPLOAD_PATH;

    /**
     * 公用文件夹Id
     */
    String CLOUD_DISK_PUBLIC_DOCUMENT = "1391657516471762945";

    /**
     * 网盘根文件夹Id
     */
    String CLOUD_DISK_ROOT_DOCUMENT = "1381437635604369410";

    /**
     * 默认登录角色信息
     */
    AuthUser DEFAULT_AUTH_USER = new AuthUser(CLIENT_ADMIN,ADMIN_USER_ID,SUPER_ADMIN_ROLE,"1,2,3,4","hlc");
}
