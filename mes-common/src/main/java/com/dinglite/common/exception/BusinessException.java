package com.dinglite.common.exception;

/**
 * 业务异常
 *
 * <AUTHOR>
 * @date 2022/03/18
 */
public class BusinessException extends RuntimeException {

    private static final long serialVersionUID = 1L;

    private String message;

    private Integer code;

    public BusinessException(String message)
    {
        this.message = message;
        this.code = 500;
    }

    public BusinessException(String message, Integer code)
    {
        this.message = message;
        this.code = code;
    }

    public BusinessException(String message, Throwable e, Integer code)
    {
        super(message, e);
        this.message = message;
        this.code = code;
    }

    @Override
    public String getMessage()
    {
        return message;
    }

    public Integer getCode()
    {
        return code;
    }
}
