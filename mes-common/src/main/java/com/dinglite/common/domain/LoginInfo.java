package com.dinglite.common.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Description
 * <AUTHOR>
 * @Date 2023-12-13 17:27
 */
@Data
@Accessors(chain = true)
public class LoginInfo implements Serializable {

    /**
     * 会话编号
     */
    private String uuid;

    /**
     * 登录名称
     */
    private String username;

    /**
     * 类型值
     */
    private String typeValue;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 主机ip
     */
    private String ipAddr;

    /**
     * 登录地点
     */
    private String loginAddr;

    /**
     * 浏览器
     */
    private String browser;

    /**
     * 操作系统
     */
    private String operatingSystem;

    /**
     * 登录时间
     */
    private String loginTime;
}
