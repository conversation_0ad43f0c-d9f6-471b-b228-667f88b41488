package com.dinglite.common.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Collections;
import java.util.List;
import java.util.function.Function;

import static java.util.stream.Collectors.toList;

/**
 * 页面dto
 *
 * <AUTHOR>
 * @date 2020/12/30 15:40
 **/
@Data
@Accessors(chain = true)
public class PageDTO<T> {

    private List<T> records;

    private long total;

    private long size;

    private long current;

    public PageDTO() {
        this.records = Collections.emptyList();
        this.total = 0L;
        this.size = 10L;
        this.current = 1L;
    }

    public PageDTO(long current, long size) {
        this(current, size, 0L, Collections.emptyList());
    }

    public PageDTO(long current, long size, long total, List<T> records) {
        this.records = records;
        this.total = 0L;
        this.size = 10L;
        this.current = 1L;
        if (current > 1L) {
            this.current = current;
        }
        this.size = size;
        this.total = total;
    }

    public boolean hasPrevious() {
        return this.current > 1L;
    }

    public boolean hasNext() {
        return this.current < this.getPages();
    }

    /**
     * 的泛型转换
     *
     * @param mapper 转换函数
     * @param <R>    转换后的泛型
     * @return
     */
    public <R> PageDTO<R> convert(Function<? super T, ? extends R> mapper) {
        List<R> collect = this.getRecords().stream().map(mapper).collect(toList());
        return ((PageDTO<R>) this).setRecords(collect);
    }

    /**
     * 当前分页总页数
     */
    public long getPages() {
        if (getSize() == 0) {
            return 0L;
        }
        long pages = getTotal() / getSize();
        if (getTotal() % getSize() != 0) {
            pages++;
        }
        return pages;
    }
}
