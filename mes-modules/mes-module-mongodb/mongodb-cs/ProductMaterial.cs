﻿using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;

namespace MDC
{
    /// <summary>
    /// 产品物料追溯信息实体类，用于记录生产过程中的物料绑定和消耗数据
    /// </summary>
    /// <remarks>
    /// 本类同时支持 RabbitMQ JSON 序列化和 MongoDB BSON 存储，字段名与业务系统字典 1:1 严格匹配
    /// </remarks>
    [BsonIgnoreExtraElements] // 允许 MongoDB 文档包含未定义字段[9](@ref)
    public class ProductMaterial
    {
        /// <summary>
        /// MongoDB 文档主键（ObjectId 类型）
        /// </summary>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [JsonProperty("_id")]
        public string _id { get; set; }

        /// <summary>
        /// 目标集合名称（固定为 product_material）
        /// </summary>
        [BsonElement("collection")]
        [JsonProperty("collection")]
        public string collection { get; set; } = "product_material";

        /// <summary>
        /// 产品序列号（示例：SN202405200001）
        /// </summary>
        [BsonElement("sn")]
        [JsonProperty("sn")]
        public string sn { get; set; }

        /// <summary>
        /// 产线名称（示例：16线）
        /// </summary>
        [BsonElement("lineName")]
        [JsonProperty("lineName")]
        public string lineName { get; set; }

        /// <summary>
        /// 产线编码（示例：LINE16）
        /// </summary>
        [BsonElement("lineCode")]
        [JsonProperty("lineCode")]
        public string lineCode { get; set; }

        /// <summary>
        /// 生产工单号（示例：P2506135）
        /// </summary>
        [BsonElement("productionOrder")]
        [JsonProperty("productionOrder")]
        public string productionOrder { get; set; }

        /// <summary>
        /// 用料工序名称（示例：喷码投料）
        /// </summary>
        [BsonElement("processName")]
        [JsonProperty("processName")]
        public string processName { get; set; }

        /// <summary>
        /// 用料工序编码（示例：GX06）
        /// </summary>
        [BsonElement("processCode")]
        [JsonProperty("processCode")]
        public string processCode { get; set; }

        /// <summary>
        /// 物料名称（示例：胶铁一体）
        /// </summary>
        [BsonElement("materialName")]
        [JsonProperty("materialName")]
        public string materialName { get; set; }

        /// <summary>
        /// 物料编码（示例：YALA0068-075V1）
        /// </summary>
        [BsonElement("materialCode")]
        [JsonProperty("materialCode")]
        public string materialCode { get; set; }

        /// <summary>
        /// 物料规格（示例：72.408 * 161.54 * 0.757）
        /// </summary>
        [BsonElement("materialSpec")]
        [JsonProperty("materialSpec")]
        public string materialSpec { get; set; }

        /// <summary>
        /// 供应商名称（可为空）
        /// </summary>
        [BsonElement("supplier")]
        [JsonProperty("supplier")]
        public string supplier { get; set; }

        /// <summary>
        /// 计量单位（示例：卷/个）
        /// </summary>
        [BsonElement("unit")]
        [JsonProperty("unit")]
        public string unit { get; set; }

        /// <summary>
        /// BOM 标准用量（单位：件/产品）
        /// </summary>
        [BsonElement("bomNum")]
        [JsonProperty("bomNum")]
        public double bomNum { get; set; }

        /// <summary>
        /// 单包装数量（单位：件/箱，整型）
        /// </summary>
        [BsonElement("packageNum")]
        [JsonProperty("packageNum")]
        public int packageNum { get; set; }

        /// <summary>
        /// 物料二维码内容（Base64 或原始字符串）
        /// </summary>
        [BsonElement("qrCode")]
        [JsonProperty("qrCode")]
        public string qrCode { get; set; }

        /// <summary>
        /// 包装箱流水码（唯一标识）
        /// </summary>
        [BsonElement("boxNumber")]
        [JsonProperty("boxNumber")]
        public string boxNumber { get; set; }

        /// <summary>
        /// 生产批次号（示例：202506）
        /// </summary>
        [BsonElement("lotNo")]
        [JsonProperty("lotNo")]
        public string lotNo { get; set; }

        /// <summary>
        /// 物料生产日期（UTC 时间）
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonElement("productDate")]
        [JsonProperty("productDate")]
        public DateTime productDate { get; set; }

        /// <summary>
        /// 物料有效期（UTC 时间，未设置时用 DateTime.MinValue 表示）
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonElement("expirationDate")]
        [JsonProperty("expirationDate")]
        public DateTime expirationDate { get; set; } = DateTime.MinValue;

        /// <summary>
        /// 上料工站 IP 地址（示例：************）
        /// </summary>
        [BsonElement("stationIP")]
        [JsonProperty("stationIP")]
        public string stationIP { get; set; }

        /// <summary>
        /// 上料工站名称（示例：16线喷码投料）
        /// </summary>
        [BsonElement("stationName")]
        [JsonProperty("stationName")]
        public string stationName { get; set; }

        /// <summary>
        /// 操作员系统账号（可为空）
        /// </summary>
        [BsonElement("operatorId")]
        [JsonProperty("operatorId")]
        public string operatorId { get; set; }

        /// <summary>
        /// 操作员姓名（可为空）
        /// </summary>
        [BsonElement("operatorName")]
        [JsonProperty("operatorName")]
        public string operatorName { get; set; }

        /// <summary>
        /// 数据提交时间（UTC 时间）
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonElement("submitTime")]
        [JsonProperty("submitTime")]
        public DateTime submitTime { get; set; }

        /// <summary>
        /// 物料启用时间（UTC 时间）
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonElement("startTime")]
        [JsonProperty("startTime")]
        public DateTime startTime { get; set; }

        /// <summary>
        /// 物料绑定时间（UTC 时间）
        /// </summary>
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonElement("createTime")]
        [JsonProperty("createTime")]
        public DateTime createTime { get; set; }

        /// <summary>
        /// 物料绑定序号（示例：500）
        /// </summary>
        [BsonElement("bindIndex")]
        [JsonProperty("bindIndex")]
        public int bindIndex { get; set; }
    }
}
