﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MongoDB.Bson;
using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;

namespace MDC.Models
{

    /// <summary>
    /// 包装信息数据模型
    /// </summary>
    public class PackageInfo
    {
        /// <summary>
        /// MongoDB自动生成的唯一标识符
        /// </summary>
        /// <remarks>
        /// 格式：ObjectId("685e6dde5cac51d9fb0b6901")\n
        /// 示例："685e6dde5cac51d9fb0b6901"
        /// </remarks>
        [BsonId]
        [BsonRepresentation(BsonType.ObjectId)]
        [JsonProperty("_id")]
        [JsonConverter(typeof(ObjectIdNullableConverter))] // 自定义转换器
        public ObjectId? _id { get; set; }
        /// <summary>
        /// 目标集合名称（动态指定存储位置）
        /// </summary>
        [BsonElement("collection")]
        [JsonProperty("collection")]
        public string collection { get; set; }

        /// <summary>
        /// 包装层级类型
        /// </summary>
        /// <remarks>
        /// 可选值：["小包", "内箱", "外箱", "栈板"]\n
        /// 示例："外箱"
        /// </remarks>
        [BsonElement("packageType")]
        [JsonProperty("packageType")]
        public string packageType { get; set; }

        /// <summary>
        /// 包装唯一序列号
        /// </summary>
        /// <remarks>
        /// 格式："OUTER-20240520001"\n
        /// 示例："OUTER-20240520001"
        /// </remarks>
        [BsonElement("serialNo")]
        [JsonProperty("serialNo")]
        public string serialNo { get; set; }

        /// <summary>
        /// 包装二维码全文本
        /// </summary>
        /// <remarks>
        /// 包含公司名/物料名/批次号等复合信息\n
        /// 示例："深圳市恒晟智造科技##胶铁一体##YALA0068-075V1##P2506100##202506##PC5000A灰色(玻纤≤3%)+SUS304##系统批号A##72.408 * 161.54 * 0.757##1100##2025.06.09##YALA0068-075V1HXP2506100000902"
        /// </remarks>
        [BsonElement("qrCode")]
        [JsonProperty("qrCode")]
        public string qrCode { get; set; }

        /// <summary>
        /// 产线名称
        /// </summary>
        [BsonElement("lineName")]
        [JsonProperty("lineName")]
        public string lineName { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        [BsonElement("lineCode")]
        [JsonProperty("lineCode")]
        public string lineCode { get; set; }

        ///// <summary>
        ///// 物料名称
        ///// </summary>
        //[BsonElement("modelName")]
        //[JsonProperty("modelName")]
        //public string modelName { get; set; }

        /// <summary>
        /// 物料编码
        /// </summary>
        [BsonElement("modelCode")]
        [JsonProperty("modelCode")]
        public string modelCode { get; set; }

        /// <summary>
        /// 生产工单编号
        /// </summary>
        /// <remarks>
        /// 格式："WO-YYYYMMDDXXX"\n
        /// 示例："WO-20240520001"
        /// </remarks>
        [BsonElement("productionOrder")]
        [JsonProperty("productionOrder")]
        public string productionOrder { get; set; }

        /// <summary>
        /// 客户料号
        /// </summary>
        [BsonElement("customerPN")]
        [JsonProperty("customerPN")]
        public string customerPN { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [BsonElement("customerOrder")]
        [JsonProperty("customerOrder")]
        public string customerOrder { get; set; }

        /// <summary>
        /// 物料包装数量
        /// </summary>
        /// <remarks>
        /// 单位：件\n
        /// 示例：1100
        /// </remarks>
        [BsonElement("packageNum")]
        [JsonProperty("packageNum")]
        public int packageNum { get; set; }

        /// <summary>
        /// 批次号
        /// </summary>
        [BsonElement("lotNo")]
        [JsonProperty("lotNo")]
        public string lotNo { get; set; }

        /// <summary>
        /// 生产日期
        /// </summary>
        [BsonElement("productionDate")]
        [JsonProperty("productionDate")]
        public DateTime productionDate { get; set; }

        /// <summary>
        /// 有效截止日期
        /// </summary>
        /// <remarks>
        /// 特殊值：0001-01-01 表示永久有效\n
        /// 示例：0001-01-01T00:00:00.000Z
        /// </remarks>
        [BsonElement("expirationDate")]
        [JsonProperty("expirationDate")]
        public DateTime expirationDate { get; set; }

        /// <summary>
        /// 操作员账号
        /// </summary>
        [BsonElement("operatorId")]
        [JsonProperty("operatorId")]
        public string operatorId { get; set; }

        /// <summary>
        /// 操作员姓名
        /// </summary>
        [BsonElement("operatorName")]
        [JsonProperty("operatorName")]
        public string operatorName { get; set; }

        /// <summary>
        /// 创建时间
        /// </summary>
        [BsonElement("createTime")]
        [JsonProperty("createTime")]
        public DateTime createTime { get; set; }

        /// <summary>
        /// 开始时间
        /// </summary>
        [BsonElement("startTime")]
        [JsonProperty("startTime")]
        public DateTime startTime { get; set; }

        /// <summary>
        /// 结束时间
        /// </summary>
        [BsonElement("endTime")]
        [JsonProperty("endTime")]
        public DateTime endTime { get; set; }

        /// <summary>
        /// 工站名称
        /// </summary>
        [BsonElement("stationName")]
        [JsonProperty("stationName")]
        public string stationName { get; set; }

        /// <summary>
        /// 工站IP地址
        /// </summary>
        [BsonElement("stationIP")]
        [JsonProperty("stationIP")]
        public string stationIP { get; set; }

        /// <summary>
        /// 扫描码集合
        /// </summary>
        /// <remarks>
        /// 格式：["SN202405200001", "PKG-INNER-001"]\n
        /// 示例：["SN202405200001", "SN202405200002"]
        /// </remarks>
        [BsonElement("scannedCodes")]
        [JsonProperty("scannedCodes")]
        public List<string> scannedCodes { get; set; }

        /// <summary>
        /// 包装状态
        /// </summary>
        /// <remarks>
        /// 可选值：["打包中", "已入库", "已出库"]\n
        /// 示例："已出库"
        /// </remarks>
        [BsonElement("status")]
        [JsonProperty("status")]
        public string status { get; set; }

        /// <summary>
        /// 入库时间
        /// </summary>
        [BsonElement("inboundTime")]
        [JsonProperty("inboundTime")]
        public DateTime inboundTime { get; set; }

        /// <summary>
        /// 出库时间
        /// </summary>
        [BsonElement("outboundTime")]
        [JsonProperty("outboundTime")]
        public DateTime outboundTime { get; set; }

        /// <summary>
        /// 关联包装层级关系
        /// </summary>
        /// <remarks>
        /// 描述当前包装包含的下级包装
        /// </remarks>
        [BsonElement("relatedPackages")]
        [JsonProperty("relatedPackages")]
        public RelatedPackages relatedPackages { get; set; }
    }

    /// <summary>
    /// 关联包装层级关系
    /// </summary>
    /// <remarks>
    /// 描述当前包装包含的下级包装序列号
    /// </remarks>
    public class RelatedPackages
    {
        /// <summary>
        /// 小包编码列表
        /// </summary>
        /// <remarks>
        /// 格式：["PKG-SMALL-001", "PKG-SMALL-002"]\n
        /// 示例：["SMALL-20240520001"]
        /// </remarks>
        [BsonElement("small")]
        [JsonProperty("small")]
        public List<string> small { get; set; }

        /// <summary>
        /// 内箱编码列表
        /// </summary>
        /// <remarks>
        /// 格式：["INNER-BOX-101", "INNER-BOX-102"]\n
        /// 示例：["INNER-20240520001"]
        /// </remarks>
        [BsonElement("inner")]
        [JsonProperty("inner")]
        public List<string> inner { get; set; }

        /// <summary>
        /// 外箱编码列表
        /// </summary>
        /// <remarks>
        /// 格式：["OUTER-BOX-202"]\n
        /// 示例：["OUTER-20240520001"]
        /// </remarks>
        [BsonElement("outer")]
        [JsonProperty("outer")]
        public List<string> outer { get; set; }

        /// <summary>
        /// 栈板编码
        /// </summary>
        /// <remarks>
        /// 格式："PALLET-505"\n
        /// 示例："PALLET-20240520001"
        /// </remarks>
        [BsonElement("pallet")]
        [JsonProperty("pallet")]
        public string pallet { get; set; }
    }

    /// <summary>
    /// 包装类型枚举
    /// </summary>
    public enum PackageType
    {
        小包 = 0,
        内箱 = 1,
        外箱 = 2,
        栈板 = 3
    }
}
