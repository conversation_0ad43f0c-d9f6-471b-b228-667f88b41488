﻿using MongoDB.Bson.Serialization.Attributes;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MDC
{
    /// <summary>
    /// 产品基础信息实体类（MongoDB 存储对象）
    /// </summary>
    /// <remarks>
    /// 映射到 MongoDB 集合 product_info，字段命名采用驼峰式匹配 BSON 文档结构[1,2](@ref)
    /// </remarks>
    [BsonIgnoreExtraElements] // 忽略文档中未定义的字段
    public class ProductInfo
    {
        ///// <summary>
        ///// MongoDB 文档唯一标识（ObjectId）
        ///// </summary>
        ///// <remarks>对应 BSON 文档中的 _id 字段[1,2](@ref)</remarks>
        //[BsonId]
        //[BsonRepresentation(BsonType.ObjectId)]
        //public ObjectId _id { get; set; }

        /// <summary>
        /// 集合名称
        /// </summary>
        /// <remarks>对应 BSON 文档中的 collection 字段</remarks>
        [BsonElement("collection")]
        [BsonRequired] // 强制字段非空[8](@ref)
        public string collection { get; set; }

        /// <summary>
        /// 产品序列号（业务主键）
        /// </summary>
        /// <remarks>对应 BSON 文档中的 sn 字段</remarks>
        [BsonElement("sn")]
        [BsonRequired] // 强制字段非空[8](@ref)
        public string sn { get; set; }

        /// <summary>
        /// 产品型号名称
        /// </summary>
        [BsonElement("modelName")]
        public string modelName { get; set; }

        /// <summary>
        /// 产品型号编码
        /// </summary>
        [BsonElement("modelCode")]
        public string modelCode { get; set; }

        /// <summary>
        /// 客户物料号
        /// </summary>
        [BsonElement("customerPN")]
        public string customerPN { get; set; }

        /// <summary>
        /// 客户订单号
        /// </summary>
        [BsonElement("customerOrder")]
        public string customerOrder { get; set; }

        /// <summary>
        /// 生产工单号
        /// </summary>
        [BsonElement("productionOrder")]
        public string productionOrder { get; set; }

        /// <summary>
        /// 生产车间名称
        /// </summary>
        [BsonElement("workshopName")]
        public string workshopName { get; set; }

        /// <summary>
        /// 产线名称
        /// </summary>
        [BsonElement("lineName")]
        public string lineName { get; set; }

        /// <summary>
        /// 产线编码
        /// </summary>
        [BsonElement("lineCode")]
        public string lineCode { get; set; }

        /// <summary>
        /// 产品状态
        /// </summary>
        /// <remarks>直通品/待复判/复判良品/复判返修/复判报废/返修中/返修良品/返修报废</remarks>
        [BsonElement("status")]
        public string status { get; set; }

        /// <summary>
        /// 是否直通品标识
        /// </summary>
        [BsonElement("isDirectProduct")]
        public bool isDirectProduct { get; set; }

        /// <summary>
        /// 投产时间（UTC 时间）
        /// </summary>
        [BsonElement("startTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)] // 明确存储为 UTC 时间[7](@ref)
        public DateTime startTime { get; set; }

        /// <summary>
        /// 最后工序名称
        /// </summary>
        [BsonElement("lastProcess")]
        public string lastProcess { get; set; }

        /// <summary>
        /// 已完成工序列表
        /// </summary>
        /// <remarks>以数组形式存储在文档中[5](@ref)</remarks>
        [BsonElement("completedProcesses")]
        public List<string> completedProcesses { get; set; }

        /// <summary>
        /// 最后工站名称
        /// </summary>
        [BsonElement("lastStation")]
        public string lastStation { get; set; }


        /// <summary>
        /// 包装状态
        /// </summary>
        /// <remarks>未包装/打小包中/已入小包/包内箱中/已入内箱/包外箱中/已入外箱/垒托中/已垒托</remarks>
        [BsonElement("packageStatus")]
        public string packageStatus { get; set; }

        /// <summary>
        /// 小包装编码
        /// </summary>
        [BsonElement("packageCode")]
        public string packageCode { get; set; }

        /// <summary>
        /// 内箱编码
        /// </summary>
        [BsonElement("innerBoxCode")]
        public string innerBoxCode { get; set; }

        /// <summary>
        /// 外箱编码
        /// </summary>
        [BsonElement("outerBoxCode")]
        public string outerBoxCode { get; set; }

        /// <summary>
        /// 栈板编码
        /// </summary>
        [BsonElement("palletCode")]
        public string palletCode { get; set; }

        /// <summary>
        /// 入库时间（UTC 时间，可为空）
        /// </summary>
        [BsonElement("inboundTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonIgnoreIfDefault] // 空值不存储[2](@ref)
        public DateTime? inboundTime { get; set; }

        /// <summary>
        /// 出库时间（UTC 时间，可为空）
        /// </summary>
        [BsonElement("outboundTime")]
        [BsonDateTimeOptions(Kind = DateTimeKind.Utc)]
        [BsonIgnoreIfDefault]
        public DateTime? outboundTime { get; set; }

    }

    /// <summary>
    /// 产品状态枚举
    /// </summary>
    public enum ProductStatus
    {
        直通品 = 0, // 直通品
        待复判 = 1, // 待复判
        复判良品 = 2, // 复判良品
        复判返修 = 3, // 复判返修
        复判报废 = 4, // 复判报废
        返修中 = 5, // 返修中
        返修良品 = 6, // 返修良品
        返修报废 = 7 // 返修报废
    }

    /// <summary>
    /// 产品包装状态枚举
    /// </summary>
    public enum PackageStatus
    {
        未包装 = 0, // 未包装
        包小包中 = 1, // 打小包中
        已包小包 = 2, // 已入小包
        包内箱中 = 3, // 包内箱中
        已包内箱 = 4, // 已入内箱
        包外箱中 = 5, // 包外箱中
        已包外箱 = 6, // 已入外箱
        垒托中 = 7, // 垒托中
        已垒托 = 8, // 已垒托
        入库中 = 9, // 入库中
        已入库 = 10, // 已入库
        出库中 = 11, // 出库中
        已出库 = 12 // 已出库
    }
}
