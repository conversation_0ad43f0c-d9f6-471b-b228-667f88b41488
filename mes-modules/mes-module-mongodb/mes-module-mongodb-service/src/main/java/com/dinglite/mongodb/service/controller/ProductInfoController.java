package com.dinglite.mongodb.service.controller;

import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.api.domain.dto.ProductInfoDTO;
import com.dinglite.mongodb.api.domain.query.ProductInfoQuery;
import com.dinglite.mongodb.service.service.ProductInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品基础信息控制器
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Api(tags = "产品基础信息管理")
@RestController
@RequestMapping(MongodbApiConstant.PRODUCT_INFO_API_PATH)
@RequiredArgsConstructor
public class ProductInfoController {

    private final ProductInfoService productInfoService;

    @ApiOperation("创建产品基础信息")
    @PostMapping
    @LoadPayload
    public ProductInfoDTO create(@RequestBody ProductInfoDTO productInfoDTO) {
        return productInfoService.create(productInfoDTO);
    }

    @ApiOperation("批量创建产品基础信息")
    @PostMapping("/batch")
    @LoadPayload
    public Integer batchCreate(@RequestBody List<ProductInfoDTO> productInfoDTOList) {
        return productInfoService.batchCreate(productInfoDTOList);
    }

    @ApiOperation("根据ID获取产品基础信息")
    @GetMapping("/{id}")
    public ProductInfoDTO getById(@ApiParam("记录ID") @PathVariable String id) {
        return productInfoService.getById(id);
    }

    @ApiOperation("根据产品序列号获取产品信息")
    @GetMapping("/sn/{sn}")
    public ProductInfoDTO getBySn(@ApiParam("产品序列号") @PathVariable String sn) {
        return productInfoService.getBySn(sn);
    }

    @ApiOperation("根据产品序列号列表获取产品信息")
    @PostMapping("/sn/list")
    public List<ProductInfoDTO> getBySnList(@RequestBody List<String> snList) {
        return productInfoService.getBySnList(snList);
    }

    @ApiOperation("更新产品基础信息")
    @PutMapping
    @LoadPayload
    public Boolean update(@RequestBody ProductInfoDTO productInfoDTO) {
        return productInfoService.update(productInfoDTO);
    }

    @ApiOperation("根据ID删除产品基础信息")
    @DeleteMapping("/{id}")
    public Boolean deleteById(@ApiParam("记录ID") @PathVariable String id) {
        return productInfoService.deleteById(id);
    }

    @ApiOperation("批量删除产品基础信息")
    @DeleteMapping("/batch")
    public Boolean batchDelete(@RequestBody List<String> ids) {
        return productInfoService.batchDelete(ids);
    }

    @ApiOperation("分页查询产品基础信息")
    @PostMapping("/page")
    @LoadPayload
    public PageDTO<ProductInfoDTO> page(@RequestBody ProductInfoQuery query) {
        return productInfoService.page(query);
    }

    @ApiOperation("根据查询条件获取记录列表")
    @PostMapping("/list")
    @LoadPayload
    public List<ProductInfoDTO> list(@RequestBody ProductInfoQuery query) {
        return productInfoService.list(query);
    }

    @ApiOperation("根据查询条件统计记录数量")
    @PostMapping("/count")
    @LoadPayload
    public Long count(@RequestBody ProductInfoQuery query) {
        return productInfoService.count(query);
    }

    @ApiOperation("统计指定产线的产品数量")
    @GetMapping("/count/line/{lineCode}")
    public Long countByLineCode(@ApiParam("产线编码") @PathVariable String lineCode) {
        return productInfoService.countByLineCode(lineCode);
    }

    @ApiOperation("统计指定状态的产品数量")
    @GetMapping("/count/status/{status}")
    public Long countByStatus(@ApiParam("产品状态") @PathVariable String status) {
        return productInfoService.countByStatus(status);
    }

    @ApiOperation("统计指定时间范围内投产的产品数量")
    @GetMapping("/count/time")
    public Long countByTimeRange(
            @ApiParam("开始时间") @RequestParam String startTime,
            @ApiParam("结束时间") @RequestParam String endTime) {
        return productInfoService.countByTimeRange(
                LocalDateTime.parse(startTime), LocalDateTime.parse(endTime));
    }

    @ApiOperation("检查产品序列号是否存在")
    @GetMapping("/exists/{sn}")
    public Boolean existsBySn(@ApiParam("产品序列号") @PathVariable String sn) {
        return productInfoService.existsBySn(sn);
    }

    @ApiOperation("根据包装编码获取产品信息")
    @GetMapping("/package/{packageCode}")
    public List<ProductInfoDTO> getByPackageCode(@ApiParam("包装编码") @PathVariable String packageCode) {
        return productInfoService.getByPackageCode(packageCode);
    }

    @ApiOperation("根据内箱编码获取产品信息")
    @GetMapping("/inner-box/{innerBoxCode}")
    public List<ProductInfoDTO> getByInnerBoxCode(@ApiParam("内箱编码") @PathVariable String innerBoxCode) {
        return productInfoService.getByInnerBoxCode(innerBoxCode);
    }

    @ApiOperation("根据外箱编码获取产品信息")
    @GetMapping("/outer-box/{outerBoxCode}")
    public List<ProductInfoDTO> getByOuterBoxCode(@ApiParam("外箱编码") @PathVariable String outerBoxCode) {
        return productInfoService.getByOuterBoxCode(outerBoxCode);
    }

    @ApiOperation("根据栈板编码获取产品信息")
    @GetMapping("/pallet/{palletCode}")
    public List<ProductInfoDTO> getByPalletCode(@ApiParam("栈板编码") @PathVariable String palletCode) {
        return productInfoService.getByPalletCode(palletCode);
    }

    @ApiOperation("更新产品状态")
    @PutMapping("/status/{sn}")
    public Boolean updateStatus(
            @ApiParam("产品序列号") @PathVariable String sn,
            @ApiParam("新状态") @RequestParam String status) {
        return productInfoService.updateStatus(sn, status);
    }

    @ApiOperation("更新产品包装状态")
    @PutMapping("/package-status/{sn}")
    public Boolean updatePackageStatus(
            @ApiParam("产品序列号") @PathVariable String sn,
            @ApiParam("包装状态") @RequestParam String packageStatus) {
        return productInfoService.updatePackageStatus(sn, packageStatus);
    }

    @ApiOperation("更新产品最后工序信息")
    @PutMapping("/last-process/{sn}")
    public Boolean updateLastProcess(
            @ApiParam("产品序列号") @PathVariable String sn,
            @ApiParam("最后工序") @RequestParam String lastProcess,
            @ApiParam("最后工站") @RequestParam String lastStation) {
        return productInfoService.updateLastProcess(sn, lastProcess, lastStation);
    }

    @ApiOperation("添加已完成工序")
    @PutMapping("/completed-process/{sn}")
    public Boolean addCompletedProcess(
            @ApiParam("产品序列号") @PathVariable String sn,
            @ApiParam("工序编码") @RequestParam String processCode) {
        return productInfoService.addCompletedProcess(sn, processCode);
    }
}
