package com.dinglite.mongodb.service.repository;

import com.dinglite.mongodb.api.domain.entity.ProductInfo;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.mongodb.repository.MongoRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 产品基础信息Repository接口
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Repository
public interface ProductInfoRepository extends MongoRepository<ProductInfo, String> {

    /**
     * 根据产品序列号查询产品信息
     * 
     * @param sn 产品序列号
     * @return 产品信息
     */
    Optional<ProductInfo> findBySn(String sn);

    /**
     * 根据产品序列号列表查询产品信息
     * 
     * @param snList 产品序列号列表
     * @return 产品信息列表
     */
    List<ProductInfo> findBySnIn(List<String> snList);

    /**
     * 根据产品型号编码查询产品信息
     * 
     * @param modelCode 产品型号编码
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByModelCode(String modelCode, Pageable pageable);

    /**
     * 根据产品型号名称查询产品信息
     * 
     * @param modelName 产品型号名称
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByModelNameContaining(String modelName, Pageable pageable);

    /**
     * 根据客户订单号查询产品信息
     * 
     * @param customerOrder 客户订单号
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByCustomerOrder(String customerOrder, Pageable pageable);

    /**
     * 根据生产工单号查询产品信息
     * 
     * @param productionOrder 生产工单号
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByProductionOrder(String productionOrder, Pageable pageable);

    /**
     * 根据产线编码查询产品信息
     * 
     * @param lineCode 产线编码
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByLineCode(String lineCode, Pageable pageable);

    /**
     * 根据产线名称查询产品信息
     * 
     * @param lineName 产线名称
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByLineNameContaining(String lineName, Pageable pageable);

    /**
     * 根据产品状态查询产品信息
     * 
     * @param status 产品状态
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByStatus(String status, Pageable pageable);

    /**
     * 根据是否直通品查询产品信息
     * 
     * @param isDirectProduct 是否直通品
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByIsDirectProduct(Boolean isDirectProduct, Pageable pageable);

    /**
     * 根据投产时间范围查询产品信息
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime, Pageable pageable);

    /**
     * 根据最后工序查询产品信息
     * 
     * @param lastProcess 最后工序
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByLastProcess(String lastProcess, Pageable pageable);

    /**
     * 根据最后工站查询产品信息
     * 
     * @param lastStation 最后工站
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByLastStation(String lastStation, Pageable pageable);

    /**
     * 根据包装状态查询产品信息
     * 
     * @param packageStatus 包装状态
     * @param pageable 分页参数
     * @return 分页产品信息
     */
    Page<ProductInfo> findByPackageStatus(String packageStatus, Pageable pageable);

    /**
     * 根据小包装编码查询产品信息
     * 
     * @param packageCode 小包装编码
     * @return 产品信息列表
     */
    List<ProductInfo> findByPackageCode(String packageCode);

    /**
     * 根据内箱编码查询产品信息
     * 
     * @param innerBoxCode 内箱编码
     * @return 产品信息列表
     */
    List<ProductInfo> findByInnerBoxCode(String innerBoxCode);

    /**
     * 根据外箱编码查询产品信息
     * 
     * @param outerBoxCode 外箱编码
     * @return 产品信息列表
     */
    List<ProductInfo> findByOuterBoxCode(String outerBoxCode);

    /**
     * 根据栈板编码查询产品信息
     * 
     * @param palletCode 栈板编码
     * @return 产品信息列表
     */
    List<ProductInfo> findByPalletCode(String palletCode);

    /**
     * 统计指定产线的产品数量
     * 
     * @param lineCode 产线编码
     * @return 产品数量
     */
    long countByLineCode(String lineCode);

    /**
     * 统计指定状态的产品数量
     * 
     * @param status 产品状态
     * @return 产品数量
     */
    long countByStatus(String status);

    /**
     * 统计指定时间范围内投产的产品数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品数量
     */
    long countByStartTimeBetween(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 检查产品序列号是否存在
     * 
     * @param sn 产品序列号
     * @return 是否存在
     */
    boolean existsBySn(String sn);
}
