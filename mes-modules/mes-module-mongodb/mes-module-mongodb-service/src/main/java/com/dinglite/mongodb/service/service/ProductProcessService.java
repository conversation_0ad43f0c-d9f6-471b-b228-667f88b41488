package com.dinglite.mongodb.service.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.entity.ProductProcess;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.service.repository.ProductProcessCustomRepository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 产品过站记录服务接口
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
public interface ProductProcessService {

    /**
     * 创建产品过站记录
     * 
     * @param productProcessDTO 产品过站记录DTO
     * @return 创建的记录DTO
     */
    ProductProcessDTO create(ProductProcessDTO productProcessDTO);

    /**
     * 批量创建产品过站记录
     * 
     * @param productProcessDTOList 产品过站记录DTO列表
     * @return 创建成功的数量
     */
    Integer batchCreate(List<ProductProcessDTO> productProcessDTOList);

    /**
     * 根据ID获取产品过站记录
     * 
     * @param id 记录ID
     * @return 产品过站记录DTO
     */
    ProductProcessDTO getById(String id);

    /**
     * 根据产品序列号获取过站记录列表
     * 
     * @param sn 产品序列号
     * @return 过站记录列表
     */
    List<ProductProcessDTO> getBySn(String sn);

    /**
     * 根据产品序列号获取最新的过站记录
     * 
     * @param sn 产品序列号
     * @return 最新过站记录
     */
    ProductProcessDTO getLatestBySn(String sn);

    /**
     * 根据产品序列号获取首道工序记录
     * 
     * @param sn 产品序列号
     * @return 首道工序记录
     */
    ProductProcessDTO getFirstProcessBySn(String sn);

    /**
     * 根据产品序列号和工序编码获取过站记录
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 过站记录
     */
    ProductProcessDTO getBySnAndProcessCode(String sn, String processCode);

    /**
     * 更新产品过站记录
     * 
     * @param productProcessDTO 产品过站记录DTO
     * @return 是否更新成功
     */
    Boolean update(ProductProcessDTO productProcessDTO);

    /**
     * 根据ID删除产品过站记录
     * 
     * @param id 记录ID
     * @return 是否删除成功
     */
    Boolean deleteById(String id);

    /**
     * 批量删除产品过站记录
     * 
     * @param ids 记录ID列表
     * @return 是否删除成功
     */
    Boolean batchDelete(List<String> ids);

    /**
     * 分页查询产品过站记录
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    PageDTO<ProductProcessDTO> page(ProductProcessQuery query);

    /**
     * 根据查询条件获取记录列表
     * 
     * @param query 查询条件
     * @return 记录列表
     */
    List<ProductProcessDTO> list(ProductProcessQuery query);

    /**
     * 根据查询条件统计记录数量
     * 
     * @param query 查询条件
     * @return 记录数量
     */
    Long count(ProductProcessQuery query);

    /**
     * 获取指定产线的过站统计信息
     * 
     * @param lineCode 产线编码
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计信息
     */
    List<ProductProcessCustomRepository.ProcessStatistics> getProcessStatistics(String lineCode, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定产线的过站记录数量
     * 
     * @param lineCode 产线编码
     * @return 记录数量
     */
    Long countByLineCode(String lineCode);

    /**
     * 统计指定时间范围内的过站记录数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数量
     */
    Long countByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 统计指定测试结果的记录数量
     * 
     * @param result 测试结果
     * @return 记录数量
     */
    Long countByResult(String result);

    /**
     * 删除指定时间之前的过站记录
     * 
     * @param beforeTime 时间点
     * @return 删除的记录数量
     */
    Long deleteByTimeBefore(LocalDateTime beforeTime);

    /**
     * 检查产品是否已过指定工序
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 是否已过工序
     */
    Boolean hasPassedProcess(String sn, String processCode);

    /**
     * 获取产品的过站路径
     * 
     * @param sn 产品序列号
     * @return 过站路径（工序编码列表）
     */
    List<String> getProcessPath(String sn);

    /**
     * 实体转DTO
     * 
     * @param productProcess 实体对象
     * @return DTO对象
     */
    ProductProcessDTO toDTO(ProductProcess productProcess);

    /**
     * DTO转实体
     * 
     * @param productProcessDTO DTO对象
     * @return 实体对象
     */
    ProductProcess toEntity(ProductProcessDTO productProcessDTO);

    /**
     * 实体列表转DTO列表
     * 
     * @param productProcessList 实体列表
     * @return DTO列表
     */
    List<ProductProcessDTO> toDTOList(List<ProductProcess> productProcessList);
}
