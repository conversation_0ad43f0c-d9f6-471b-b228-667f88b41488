package com.dinglite.mongodb.service.config;

import com.mongodb.ConnectionString;
import com.mongodb.MongoClientSettings;
import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.convert.DefaultMongoTypeMapper;
import org.springframework.data.mongodb.core.convert.MappingMongoConverter;
import org.springframework.data.mongodb.core.convert.MongoCustomConversions;

import java.util.concurrent.TimeUnit;

/**
 * MongoDB配置类
 *
 * <AUTHOR>
 * @since 2025-07-31
 */
@Configuration
public class MongodbConfig extends AbstractMongoClientConfiguration {

    @Value("${spring.data.mongodb.database:mes_mongodb}")
    private String database;

    @Value("${spring.data.mongodb.host:127.0.0.1}")
    private String host;

    @Value("${spring.data.mongodb.port:27017}")
    private int port;

    @Value("${spring.data.mongodb.username:}")
    private String username;

    @Value("${spring.data.mongodb.password:}")
    private String password;

    @Value("${spring.data.mongodb.authentication-database:admin}")
    private String authDatabase;

    @Override
    protected String getDatabaseName() {
        return database;
    }

    @Override
    @Bean
    public MongoClient mongoClient() {
        // 构建连接字符串
        String connectionString;
        if (username != null && !username.trim().isEmpty() &&
            password != null && !password.trim().isEmpty()) {
            connectionString = String.format("mongodb://%s:%s@%s:%d/%s?authSource=%s",
                username, password, host, port, database, authDatabase);
        } else {
            connectionString = String.format("mongodb://%s:%d/%s", host, port, database);
        }

        MongoClientSettings settings = MongoClientSettings.builder()
                .applyConnectionString(new ConnectionString(connectionString))
                .applyToConnectionPoolSettings(builder ->
                    builder.maxSize(100)
                           .minSize(10)
                           .maxWaitTime(120, TimeUnit.SECONDS)
                           .maxConnectionIdleTime(0, TimeUnit.MILLISECONDS)
                           .maxConnectionLifeTime(0, TimeUnit.MILLISECONDS))
                .applyToSocketSettings(builder ->
                    builder.connectTimeout(10, TimeUnit.SECONDS)
                           .readTimeout(0, TimeUnit.MILLISECONDS))
                .applyToServerSettings(builder ->
                    builder.heartbeatFrequency(10, TimeUnit.SECONDS)
                           .minHeartbeatFrequency(500, TimeUnit.MILLISECONDS))
                .build();

        return MongoClients.create(settings);
    }

    @Bean
    public MongoTemplate mongoTemplate() throws Exception {
        MongoTemplate mongoTemplate = new MongoTemplate(mongoClient(), getDatabaseName());

        // 移除_class字段
        MappingMongoConverter converter = (MappingMongoConverter) mongoTemplate.getConverter();
        converter.setTypeMapper(new DefaultMongoTypeMapper(null));

        return mongoTemplate;
    }

    @Bean
    public MongoCustomConversions customConversions() {
        return new MongoCustomConversions(java.util.Collections.emptyList());
    }
}
