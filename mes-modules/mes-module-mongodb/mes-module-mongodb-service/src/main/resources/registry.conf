registry {
  # file 、nacos 、eureka、redis、zk、consul、etcd3、sofa
  type = "nacos"
  loadBalance = "RandomLoadBalance"
  loadBalanceVirtualNodes = 10

  nacos {
    serverAddr = "127.0.0.1:8848"
    group = "DEFAULT_GROUP"
    namespace = "4d307a39-1034-4861-83fd-c6921c6d9c02"
    cluster = "default"
  }

}

config {
  # file、nacos 、apollo、zk、consul、etcd3
  type = "nacos"

  nacos {
      serverAddr = "127.0.0.1:8848"
      namespace = "4d307a39-1034-4861-83fd-c6921c6d9c02"
      group = "SEATA_1.4_GROUP"
  }
}
