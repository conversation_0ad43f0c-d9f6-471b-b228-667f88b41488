# 测试环境配置
server.port=8010
spring.application.name=mes-mongodb-test
logging.level.com.dinglite=debug

# MongoDB测试配置 - 使用内嵌MongoDB或测试数据库
spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb_test
spring.data.mongodb.username=admin
spring.data.mongodb.password=123456
spring.data.mongodb.authentication-database=admin

# MongoDB连接池配置（测试环境使用较小的连接池）
spring.data.mongodb.option.connections-per-host=10
spring.data.mongodb.option.min-connections-per-host=2
spring.data.mongodb.option.max-wait-time=30000
spring.data.mongodb.option.connect-timeout=5000
spring.data.mongodb.option.socket-timeout=0
spring.data.mongodb.option.socket-keep-alive=false

# 禁用Nacos配置中心（测试环境）
spring.cloud.nacos.config.enabled=false
spring.cloud.nacos.discovery.enabled=false

# 禁用Feign客户端（测试环境）
feign.hystrix.enabled=false

# 日志配置
logging.level.org.springframework.data.mongodb=DEBUG
logging.level.org.mongodb.driver=INFO
