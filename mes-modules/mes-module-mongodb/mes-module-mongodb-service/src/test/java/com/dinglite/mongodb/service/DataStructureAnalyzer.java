package com.dinglite.mongodb.service;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.*;

/**
 * MongoDB 数据结构分析工具
 * 用于分析生产数据库中的实际数据结构
 */
@SpringBootTest
@ActiveProfiles("test")
public class DataStructureAnalyzer {

    @Autowired
    private MongoClient mongoClient;

    @Test
    public void analyzeProductProcessStructure() {
        System.out.println("=== 分析 product_process 集合的实际数据结构 ===");
        
        try {
            // 连接到生产数据库
            MongoDatabase database = mongoClient.getDatabase("production_db");
            MongoCollection<Document> collection = database.getCollection("product_process");
            
            // 获取集合统计信息
            long count = collection.countDocuments();
            System.out.println("总文档数量: " + count);
            
            if (count == 0) {
                System.out.println("⚠️ product_process 集合为空，无法分析数据结构");
                return;
            }
            
            // 获取前5个文档样本
            System.out.println("\n=== 文档样本 ===");
            for (Document doc : collection.find().limit(5)) {
                System.out.println("样本文档:");
                printDocumentStructure(doc, "  ");
                System.out.println("---");
            }
            
            // 分析字段统计
            System.out.println("\n=== 字段统计分析 ===");
            analyzeFieldStatistics(collection);
            
        } catch (Exception e) {
            System.err.println("❌ 连接生产数据库失败: " + e.getMessage());
            System.out.println("这可能是因为:");
            System.out.println("1. 网络连接问题");
            System.out.println("2. 认证信息不正确");
            System.out.println("3. 数据库名称不匹配");
            System.out.println("4. 防火墙限制");
        }
    }
    
    @Test
    public void analyzeAllCollections() {
        System.out.println("=== 分析所有集合的数据结构 ===");
        
        String[] collections = {
            "product_process", "product_info", "product_exception", 
            "product_material", "package_info", "material_info"
        };
        
        try {
            MongoDatabase database = mongoClient.getDatabase("production_db");
            
            for (String collName : collections) {
                System.out.println("\n=== " + collName + " 集合 ===");
                MongoCollection<Document> collection = database.getCollection(collName);
                
                long count = collection.countDocuments();
                System.out.println("文档数量: " + count);
                
                if (count > 0) {
                    Document sample = collection.find().first();
                    if (sample != null) {
                        System.out.println("样本文档结构:");
                        printDocumentStructure(sample, "  ");
                    }
                } else {
                    System.out.println("⚠️ 集合为空");
                }
            }
            
        } catch (Exception e) {
            System.err.println("❌ 分析失败: " + e.getMessage());
        }
    }
    
    private void printDocumentStructure(Document doc, String indent) {
        for (Map.Entry<String, Object> entry : doc.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            String type = getValueType(value);
            
            System.out.println(indent + key + " (" + type + "): " + getValuePreview(value));
            
            // 如果是嵌套文档，递归打印
            if (value instanceof Document) {
                printDocumentStructure((Document) value, indent + "  ");
            }
        }
    }
    
    private String getValueType(Object value) {
        if (value == null) return "null";
        if (value instanceof String) return "String";
        if (value instanceof Integer) return "Integer";
        if (value instanceof Long) return "Long";
        if (value instanceof Double) return "Double";
        if (value instanceof Boolean) return "Boolean";
        if (value instanceof Date) return "Date";
        if (value instanceof Document) return "Document";
        if (value instanceof List) return "Array";
        return value.getClass().getSimpleName();
    }
    
    private String getValuePreview(Object value) {
        if (value == null) return "null";
        if (value instanceof String) {
            String str = (String) value;
            return str.length() > 50 ? "\"" + str.substring(0, 47) + "...\"" : "\"" + str + "\"";
        }
        if (value instanceof Document) {
            Document doc = (Document) value;
            return "{" + doc.keySet().size() + " fields}";
        }
        if (value instanceof List) {
            List<?> list = (List<?>) value;
            return "[" + list.size() + " items]";
        }
        return value.toString();
    }
    
    private void analyzeFieldStatistics(MongoCollection<Document> collection) {
        // 简单的字段统计（由于 MongoDB Java 驱动的限制，这里用简化版本）
        Map<String, Integer> fieldCounts = new HashMap<>();
        Map<String, Set<String>> fieldTypes = new HashMap<>();
        
        for (Document doc : collection.find().limit(100)) {
            analyzeDocumentFields(doc, "", fieldCounts, fieldTypes);
        }
        
        System.out.println("字段统计 (基于前100个文档):");
        fieldCounts.entrySet().stream()
            .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
            .forEach(entry -> {
                String field = entry.getKey();
                int count = entry.getValue();
                Set<String> types = fieldTypes.get(field);
                System.out.println("  " + field + ": 出现 " + count + " 次, 类型: " + types);
            });
    }
    
    private void analyzeDocumentFields(Document doc, String prefix, 
                                     Map<String, Integer> fieldCounts, 
                                     Map<String, Set<String>> fieldTypes) {
        for (Map.Entry<String, Object> entry : doc.entrySet()) {
            String fieldName = prefix.isEmpty() ? entry.getKey() : prefix + "." + entry.getKey();
            Object value = entry.getValue();
            String type = getValueType(value);
            
            fieldCounts.merge(fieldName, 1, Integer::sum);
            fieldTypes.computeIfAbsent(fieldName, k -> new HashSet<>()).add(type);
            
            // 递归处理嵌套文档
            if (value instanceof Document) {
                analyzeDocumentFields((Document) value, fieldName, fieldCounts, fieldTypes);
            }
        }
    }
}
