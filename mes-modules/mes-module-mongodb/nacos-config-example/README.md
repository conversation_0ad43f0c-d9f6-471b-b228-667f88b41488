# MongoDB模块Nacos配置说明

## 概述

MongoDB模块已经集成到Nacos配置中心，所有MongoDB连接配置都通过Nacos统一管理，与项目中的MySQL和Redis配置保持一致。

## Nacos配置步骤

### 1. 登录Nacos控制台
- 访问：http://localhost:8848/nacos
- 用户名：nacos
- 密码：dlt123456

### 2. 选择正确的命名空间
- 切换到命名空间：`4d307a39-1034-4861-83fd-c6921c6d9c02`
- 这是项目统一使用的命名空间

### 3. 创建MongoDB配置文件
在配置管理 -> 配置列表中，点击"+"创建新配置：

- **Data ID**: `mes-mongodb.properties`
- **Group**: `DEFAULT_GROUP`
- **配置格式**: `Properties`
- **配置内容**: 复制 `mes-mongodb.properties` 文件的内容

### 4. 配置内容说明

```properties
# MongoDB基础连接配置
spring.data.mongodb.host=*************          # MongoDB服务器地址
spring.data.mongodb.port=27017                   # MongoDB端口
spring.data.mongodb.database=mes_mongodb         # 数据库名称
spring.data.mongodb.username=admin               # 用户名
spring.data.mongodb.password=123456              # 密码
spring.data.mongodb.authentication-database=admin # 认证数据库

# MongoDB连接池配置
spring.data.mongodb.option.connections-per-host=100      # 最大连接数
spring.data.mongodb.option.min-connections-per-host=10   # 最小连接数
spring.data.mongodb.option.max-wait-time=120000          # 最大等待时间(ms)
spring.data.mongodb.option.connect-timeout=10000         # 连接超时时间(ms)
spring.data.mongodb.option.socket-timeout=0              # Socket超时时间(ms)
spring.data.mongodb.option.heartbeat-frequency=10000     # 心跳频率(ms)
```

## 配置优势

1. **统一管理**: 与MySQL、Redis配置保持一致的管理方式
2. **动态更新**: 配置修改后无需重启服务即可生效
3. **环境隔离**: 不同环境可以使用不同的配置
4. **安全性**: 敏感信息集中管理，不暴露在代码中
5. **版本控制**: Nacos提供配置版本管理和回滚功能

## 注意事项

1. 确保Nacos服务正常运行
2. 确保MongoDB服务可访问
3. 配置修改后建议先在测试环境验证
4. 生产环境配置请根据实际情况调整连接池参数

## 故障排查

如果MongoDB连接失败，请检查：
1. Nacos配置是否正确
2. MongoDB服务是否启动
3. 网络连接是否正常
4. 用户名密码是否正确
5. 防火墙设置是否允许连接
