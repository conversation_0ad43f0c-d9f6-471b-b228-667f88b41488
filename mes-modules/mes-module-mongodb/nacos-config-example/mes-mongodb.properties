# MongoDB连接配置
# 这个文件需要在Nacos配置中心创建，Data ID: mes-mongodb.properties，Group: DEFAULT_GROUP

# MongoDB基础连接配置
spring.data.mongodb.host=*************
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb
spring.data.mongodb.username=admin
spring.data.mongodb.password=123456
spring.data.mongodb.authentication-database=admin

# MongoDB连接池配置
spring.data.mongodb.option.connections-per-host=100
spring.data.mongodb.option.min-connections-per-host=10
spring.data.mongodb.option.max-wait-time=120000
spring.data.mongodb.option.max-connection-idle-time=0
spring.data.mongodb.option.max-connection-life-time=0
spring.data.mongodb.option.connect-timeout=10000
spring.data.mongodb.option.socket-timeout=0
spring.data.mongodb.option.heartbeat-frequency=10000
spring.data.mongodb.option.min-heartbeat-frequency=500

# MongoDB SSL配置（如果需要）
spring.data.mongodb.option.ssl-enabled=false
spring.data.mongodb.option.ssl-invalid-hostname-allowed=false

# MongoDB其他配置
spring.data.mongodb.option.always-use-m-beans=false
spring.data.mongodb.option.local-threshold=15
