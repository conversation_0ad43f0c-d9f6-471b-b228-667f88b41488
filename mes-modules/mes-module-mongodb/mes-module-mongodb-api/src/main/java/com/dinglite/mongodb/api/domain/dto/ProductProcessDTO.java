package com.dinglite.mongodb.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品过站记录数据传输对象
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductProcessDTO对象", description = "产品过站记录数据传输对象")
public class ProductProcessDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识符")
    @JsonProperty("id")
    private String id;

    @ApiModelProperty(value = "集合名称")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "过站时间")
    @JsonProperty("processTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTime;

    @ApiModelProperty(value = "元数据字段")
    @JsonProperty("meta")
    private MetaDataDTO meta = new MetaDataDTO();

    @ApiModelProperty(value = "治具版本")
    @JsonProperty("fixtureVersion")
    private String fixtureVersion;

    @ApiModelProperty(value = "测试结果（PASS/FAIL）")
    @JsonProperty("result")
    private String result;

    @ApiModelProperty(value = "设备参数集合")
    @JsonProperty("parameters")
    private Map<String, Object> parameters = new HashMap<>();

    @ApiModelProperty(value = "产品扫描时间")
    @JsonProperty("scanTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scanTime;

    @ApiModelProperty(value = "产线名称")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "工站IP地址")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "工站名称")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "治具名称")
    @JsonProperty("fixtureName")
    private String fixtureName;

    @ApiModelProperty(value = "数据提交时间")
    @JsonProperty("submitTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "是否为补扫记录")
    @JsonProperty("isRepairScan")
    private Boolean isRepairScan = false;

    @ApiModelProperty(value = "扫描编码")
    @JsonProperty("scanCode")
    private String scanCode;

    @ApiModelProperty(value = "不良描述列表")
    @JsonProperty("failDesc")
    private List<String> failDesc = new ArrayList<>();

    @ApiModelProperty(value = "不良名称")
    @JsonProperty("failName")
    private String failName;

    @ApiModelProperty(value = "不良编码")
    @JsonProperty("failCode")
    private String failCode;

    @ApiModelProperty(value = "操作员姓名")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "治具编码")
    @JsonProperty("fixtureCode")
    private String fixtureCode;

    @ApiModelProperty(value = "工序名称")
    @JsonProperty("processName")
    private String processName;

    @ApiModelProperty(value = "是否首道工序")
    @JsonProperty("isFirstProcess")
    private Boolean isFirstProcess;

    @ApiModelProperty(value = "重投工序编码")
    @JsonProperty("reworkProcessCode")
    private String reworkProcessCode;

    @ApiModelProperty(value = "重投工序名称")
    @JsonProperty("reworkProcessName")
    private String reworkProcessName;

    /**
     * 元数据数据传输对象
     */
    @Data
    @ApiModel(value = "MetaDataDTO对象", description = "产品过站记录元数据")
    public static class MetaDataDTO implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "产线编码")
        @JsonProperty("lineCode")
        private String lineCode;

        @ApiModelProperty(value = "操作员账号")
        @JsonProperty("operatorId")
        private String operatorId;

        @ApiModelProperty(value = "工序编码")
        @JsonProperty("processCode")
        private String processCode;

        @ApiModelProperty(value = "产品序列号")
        @JsonProperty("sn")
        private String sn;
    }
}
