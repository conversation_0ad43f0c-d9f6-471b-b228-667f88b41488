package com.dinglite.mongodb.api.service.fallback;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.api.service.ProductProcessClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品过站记录Feign客户端降级处理工厂
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Component
public class ProductProcessClientFallbackFactory implements FallbackFactory<ProductProcessClient> {

    @Override
    public ProductProcessClient create(Throwable cause) {
        log.error("ProductProcessClient调用失败，进入降级处理", cause);
        
        return new ProductProcessClient() {
            @Override
            public ProductProcessDTO create(ProductProcessDTO productProcessDTO) {
                log.error("创建产品过站记录失败，降级处理", cause);
                return null;
            }

            @Override
            public Integer batchCreate(List<ProductProcessDTO> productProcessDTOList) {
                log.error("批量创建产品过站记录失败，降级处理", cause);
                return 0;
            }

            @Override
            public ProductProcessDTO getById(String id) {
                log.error("根据ID获取产品过站记录失败，降级处理，ID: {}", id, cause);
                return null;
            }

            @Override
            public List<ProductProcessDTO> getBySn(String sn) {
                log.error("根据产品序列号获取过站记录失败，降级处理，SN: {}", sn, cause);
                return new ArrayList<>();
            }

            @Override
            public ProductProcessDTO getLatestBySn(String sn) {
                log.error("根据产品序列号获取最新过站记录失败，降级处理，SN: {}", sn, cause);
                return null;
            }

            @Override
            public ProductProcessDTO getFirstProcessBySn(String sn) {
                log.error("根据产品序列号获取首道工序记录失败，降级处理，SN: {}", sn, cause);
                return null;
            }

            @Override
            public ProductProcessDTO getBySnAndProcessCode(String sn, String processCode) {
                log.error("根据SN和工序编码获取过站记录失败，降级处理，SN: {}, ProcessCode: {}", sn, processCode, cause);
                return null;
            }

            @Override
            public Boolean update(ProductProcessDTO productProcessDTO) {
                log.error("更新产品过站记录失败，降级处理", cause);
                return false;
            }

            @Override
            public Boolean deleteById(String id) {
                log.error("删除产品过站记录失败，降级处理，ID: {}", id, cause);
                return false;
            }

            @Override
            public Boolean batchDelete(List<String> ids) {
                log.error("批量删除产品过站记录失败，降级处理", cause);
                return false;
            }

            @Override
            public PageDTO<ProductProcessDTO> page(ProductProcessQuery query) {
                log.error("分页查询产品过站记录失败，降级处理", cause);
                return new PageDTO<>();
            }

            @Override
            public List<ProductProcessDTO> list(ProductProcessQuery query) {
                log.error("查询产品过站记录列表失败，降级处理", cause);
                return new ArrayList<>();
            }

            @Override
            public Long count(ProductProcessQuery query) {
                log.error("统计产品过站记录数量失败，降级处理", cause);
                return 0L;
            }

            @Override
            public Long countByLineCode(String lineCode) {
                log.error("根据产线编码统计记录数量失败，降级处理，LineCode: {}", lineCode, cause);
                return 0L;
            }

            @Override
            public Long countByTimeRange(String startTime, String endTime) {
                log.error("根据时间范围统计记录数量失败，降级处理", cause);
                return 0L;
            }

            @Override
            public Long countByResult(String result) {
                log.error("根据测试结果统计记录数量失败，降级处理，Result: {}", result, cause);
                return 0L;
            }

            @Override
            public Boolean hasPassedProcess(String sn, String processCode) {
                log.error("检查产品是否已过工序失败，降级处理，SN: {}, ProcessCode: {}", sn, processCode, cause);
                return false;
            }

            @Override
            public List<String> getProcessPath(String sn) {
                log.error("获取产品过站路径失败，降级处理，SN: {}", sn, cause);
                return new ArrayList<>();
            }
        };
    }
}
