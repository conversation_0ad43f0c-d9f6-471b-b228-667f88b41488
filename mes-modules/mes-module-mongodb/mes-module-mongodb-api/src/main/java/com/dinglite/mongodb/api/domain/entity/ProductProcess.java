package com.dinglite.mongodb.api.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.data.mongodb.core.mapping.Field;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品过站记录模型（对应集合 product_process）
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = false)
@ApiModel(value = "ProductProcess对象", description = "产品过站记录")
@Document(collection = "product_process")
public class ProductProcess implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "唯一标识符（MongoDB ObjectId）")
    @Id
    @JsonProperty("_id")
    private String id;

    @ApiModelProperty(value = "集合名称")
    @Field("collection")
    @JsonProperty("collection")
    private String collection;

    @ApiModelProperty(value = "过站时间")
    @Field("processTime")
    @JsonProperty("processTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processTime;

    @ApiModelProperty(value = "元数据字段（用于筛选和分组）")
    @Field("meta")
    @JsonProperty("meta")
    private MetaData meta = new MetaData();

    @ApiModelProperty(value = "治具版本")
    @Field("fixtureVersion")
    @JsonProperty("fixtureVersion")
    private String fixtureVersion;

    @ApiModelProperty(value = "测试结果（PASS/FAIL）")
    @Field("result")
    @JsonProperty("result")
    private String result;

    @ApiModelProperty(value = "设备参数集合")
    @Field("parameters")
    @JsonProperty("parameters")
    private Map<String, Object> parameters = new HashMap<>();

    @ApiModelProperty(value = "产品扫描时间")
    @Field("scanTime")
    @JsonProperty("scanTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime scanTime;

    @ApiModelProperty(value = "产线名称")
    @Field("lineName")
    @JsonProperty("lineName")
    private String lineName;

    @ApiModelProperty(value = "工站IP地址")
    @Field("stationIP")
    @JsonProperty("stationIP")
    private String stationIP;

    @ApiModelProperty(value = "工站名称")
    @Field("stationName")
    @JsonProperty("stationName")
    private String stationName;

    @ApiModelProperty(value = "治具名称")
    @Field("fixtureName")
    @JsonProperty("fixtureName")
    private String fixtureName;

    @ApiModelProperty(value = "数据提交时间")
    @Field("submitTime")
    @JsonProperty("submitTime")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime submitTime;

    @ApiModelProperty(value = "是否为补扫记录")
    @Field("isRepairScan")
    @JsonProperty("isRepairScan")
    private Boolean isRepairScan = false;

    @ApiModelProperty(value = "扫描编码")
    @Field("scanCode")
    @JsonProperty("scanCode")
    private String scanCode;

    @ApiModelProperty(value = "不良描述列表")
    @Field("failDesc")
    @JsonProperty("failDesc")
    private List<String> failDesc = new ArrayList<>();

    @ApiModelProperty(value = "不良名称")
    @Field("failName")
    @JsonProperty("failName")
    private String failName;

    @ApiModelProperty(value = "不良编码")
    @Field("failCode")
    @JsonProperty("failCode")
    private String failCode;

    @ApiModelProperty(value = "操作员姓名")
    @Field("operatorName")
    @JsonProperty("operatorName")
    private String operatorName;

    @ApiModelProperty(value = "治具编码")
    @Field("fixtureCode")
    @JsonProperty("fixtureCode")
    private String fixtureCode;

    @ApiModelProperty(value = "工序名称")
    @Field("processName")
    @JsonProperty("processName")
    private String processName;

    @ApiModelProperty(value = "是否首道工序")
    @Field("isFirstProcess")
    @JsonProperty("isFirstProcess")
    private Boolean isFirstProcess;

    @ApiModelProperty(value = "重投工序编码")
    @Field("reworkProcessCode")
    @JsonProperty("reworkProcessCode")
    private String reworkProcessCode;

    @ApiModelProperty(value = "重投工序名称")
    @Field("reworkProcessName")
    @JsonProperty("reworkProcessName")
    private String reworkProcessName;

    /**
     * 表示产品过站记录中的元数据
     * 用于数据筛选和分组的关键字段
     */
    @Data
    @ApiModel(value = "MetaData对象", description = "产品过站记录元数据")
    public static class MetaData implements Serializable {

        private static final long serialVersionUID = 1L;

        @ApiModelProperty(value = "产线编码")
        @Field("lineCode")
        @JsonProperty("lineCode")
        private String lineCode;

        @ApiModelProperty(value = "操作员账号")
        @Field("operatorId")
        @JsonProperty("operatorId")
        private String operatorId;

        @ApiModelProperty(value = "工序编码")
        @Field("processCode")
        @JsonProperty("processCode")
        private String processCode;

        @ApiModelProperty(value = "产品序列号")
        @Field("sn")
        @JsonProperty("sn")
        private String sn;
    }
}
