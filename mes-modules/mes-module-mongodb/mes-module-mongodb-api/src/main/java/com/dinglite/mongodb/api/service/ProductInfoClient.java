package com.dinglite.mongodb.api.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.api.domain.dto.ProductInfoDTO;
import com.dinglite.mongodb.api.domain.query.ProductInfoQuery;
import com.dinglite.mongodb.api.service.fallback.ProductInfoClientFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品基础信息Feign客户端
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@FeignClient(name = MongodbApiConstant.FEIGN_NAME, 
             contextId = "productInfoClient", 
             fallbackFactory = ProductInfoClientFallbackFactory.class)
@RequestMapping(MongodbApiConstant.PRODUCT_INFO_API_PATH)
public interface ProductInfoClient {

    /**
     * 创建产品基础信息
     * 
     * @param productInfoDTO 产品信息DTO
     * @return 创建的记录DTO
     */
    @PostMapping
    ProductInfoDTO create(@RequestBody ProductInfoDTO productInfoDTO);

    /**
     * 批量创建产品基础信息
     * 
     * @param productInfoDTOList 产品信息DTO列表
     * @return 创建成功的数量
     */
    @PostMapping("/batch")
    Integer batchCreate(@RequestBody List<ProductInfoDTO> productInfoDTOList);

    /**
     * 根据ID获取产品基础信息
     * 
     * @param id 记录ID
     * @return 产品信息DTO
     */
    @GetMapping("/{id}")
    ProductInfoDTO getById(@PathVariable("id") String id);

    /**
     * 根据产品序列号获取产品信息
     * 
     * @param sn 产品序列号
     * @return 产品信息DTO
     */
    @GetMapping("/sn/{sn}")
    ProductInfoDTO getBySn(@PathVariable("sn") String sn);

    /**
     * 根据产品序列号列表获取产品信息
     * 
     * @param snList 产品序列号列表
     * @return 产品信息列表
     */
    @PostMapping("/sn/list")
    List<ProductInfoDTO> getBySnList(@RequestBody List<String> snList);

    /**
     * 更新产品基础信息
     * 
     * @param productInfoDTO 产品信息DTO
     * @return 是否更新成功
     */
    @PutMapping
    Boolean update(@RequestBody ProductInfoDTO productInfoDTO);

    /**
     * 根据ID删除产品基础信息
     * 
     * @param id 记录ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    Boolean deleteById(@PathVariable("id") String id);

    /**
     * 批量删除产品基础信息
     * 
     * @param ids 记录ID列表
     * @return 是否删除成功
     */
    @DeleteMapping("/batch")
    Boolean batchDelete(@RequestBody List<String> ids);

    /**
     * 分页查询产品基础信息
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    PageDTO<ProductInfoDTO> page(@RequestBody ProductInfoQuery query);

    /**
     * 根据查询条件获取记录列表
     * 
     * @param query 查询条件
     * @return 记录列表
     */
    @PostMapping("/list")
    List<ProductInfoDTO> list(@RequestBody ProductInfoQuery query);

    /**
     * 根据查询条件统计记录数量
     * 
     * @param query 查询条件
     * @return 记录数量
     */
    @PostMapping("/count")
    Long count(@RequestBody ProductInfoQuery query);

    /**
     * 统计指定产线的产品数量
     * 
     * @param lineCode 产线编码
     * @return 产品数量
     */
    @GetMapping("/count/line/{lineCode}")
    Long countByLineCode(@PathVariable("lineCode") String lineCode);

    /**
     * 统计指定状态的产品数量
     * 
     * @param status 产品状态
     * @return 产品数量
     */
    @GetMapping("/count/status/{status}")
    Long countByStatus(@PathVariable("status") String status);

    /**
     * 统计指定时间范围内投产的产品数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 产品数量
     */
    @GetMapping("/count/time")
    Long countByTimeRange(@RequestParam("startTime") String startTime, 
                         @RequestParam("endTime") String endTime);

    /**
     * 检查产品序列号是否存在
     * 
     * @param sn 产品序列号
     * @return 是否存在
     */
    @GetMapping("/exists/{sn}")
    Boolean existsBySn(@PathVariable("sn") String sn);

    /**
     * 根据包装编码获取产品信息
     * 
     * @param packageCode 包装编码
     * @return 产品信息列表
     */
    @GetMapping("/package/{packageCode}")
    List<ProductInfoDTO> getByPackageCode(@PathVariable("packageCode") String packageCode);

    /**
     * 根据内箱编码获取产品信息
     * 
     * @param innerBoxCode 内箱编码
     * @return 产品信息列表
     */
    @GetMapping("/inner-box/{innerBoxCode}")
    List<ProductInfoDTO> getByInnerBoxCode(@PathVariable("innerBoxCode") String innerBoxCode);

    /**
     * 根据外箱编码获取产品信息
     * 
     * @param outerBoxCode 外箱编码
     * @return 产品信息列表
     */
    @GetMapping("/outer-box/{outerBoxCode}")
    List<ProductInfoDTO> getByOuterBoxCode(@PathVariable("outerBoxCode") String outerBoxCode);

    /**
     * 根据栈板编码获取产品信息
     * 
     * @param palletCode 栈板编码
     * @return 产品信息列表
     */
    @GetMapping("/pallet/{palletCode}")
    List<ProductInfoDTO> getByPalletCode(@PathVariable("palletCode") String palletCode);

    /**
     * 更新产品状态
     * 
     * @param sn 产品序列号
     * @param status 新状态
     * @return 是否更新成功
     */
    @PutMapping("/status/{sn}")
    Boolean updateStatus(@PathVariable("sn") String sn, @RequestParam("status") String status);

    /**
     * 更新产品包装状态
     * 
     * @param sn 产品序列号
     * @param packageStatus 包装状态
     * @return 是否更新成功
     */
    @PutMapping("/package-status/{sn}")
    Boolean updatePackageStatus(@PathVariable("sn") String sn, 
                               @RequestParam("packageStatus") String packageStatus);

    /**
     * 更新产品最后工序信息
     * 
     * @param sn 产品序列号
     * @param lastProcess 最后工序
     * @param lastStation 最后工站
     * @return 是否更新成功
     */
    @PutMapping("/last-process/{sn}")
    Boolean updateLastProcess(@PathVariable("sn") String sn, 
                            @RequestParam("lastProcess") String lastProcess,
                            @RequestParam("lastStation") String lastStation);

    /**
     * 添加已完成工序
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 是否更新成功
     */
    @PutMapping("/completed-process/{sn}")
    Boolean addCompletedProcess(@PathVariable("sn") String sn, 
                              @RequestParam("processCode") String processCode);
}
