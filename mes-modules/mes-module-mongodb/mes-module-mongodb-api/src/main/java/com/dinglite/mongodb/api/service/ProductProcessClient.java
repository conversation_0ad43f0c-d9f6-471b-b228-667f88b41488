package com.dinglite.mongodb.api.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.constant.MongodbApiConstant;
import com.dinglite.mongodb.api.domain.dto.ProductProcessDTO;
import com.dinglite.mongodb.api.domain.query.ProductProcessQuery;
import com.dinglite.mongodb.api.service.fallback.ProductProcessClientFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 产品过站记录Feign客户端
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@FeignClient(name = MongodbApiConstant.FEIGN_NAME, 
             contextId = "productProcessClient", 
             fallbackFactory = ProductProcessClientFallbackFactory.class)
@RequestMapping(MongodbApiConstant.PRODUCT_PROCESS_API_PATH)
public interface ProductProcessClient {

    /**
     * 创建产品过站记录
     * 
     * @param productProcessDTO 产品过站记录DTO
     * @return 创建的记录DTO
     */
    @PostMapping
    ProductProcessDTO create(@RequestBody ProductProcessDTO productProcessDTO);

    /**
     * 批量创建产品过站记录
     * 
     * @param productProcessDTOList 产品过站记录DTO列表
     * @return 创建成功的数量
     */
    @PostMapping("/batch")
    Integer batchCreate(@RequestBody List<ProductProcessDTO> productProcessDTOList);

    /**
     * 根据ID获取产品过站记录
     * 
     * @param id 记录ID
     * @return 产品过站记录DTO
     */
    @GetMapping("/{id}")
    ProductProcessDTO getById(@PathVariable("id") String id);

    /**
     * 根据产品序列号获取过站记录列表
     * 
     * @param sn 产品序列号
     * @return 过站记录列表
     */
    @GetMapping("/sn/{sn}")
    List<ProductProcessDTO> getBySn(@PathVariable("sn") String sn);

    /**
     * 根据产品序列号获取最新的过站记录
     * 
     * @param sn 产品序列号
     * @return 最新过站记录
     */
    @GetMapping("/latest/{sn}")
    ProductProcessDTO getLatestBySn(@PathVariable("sn") String sn);

    /**
     * 根据产品序列号获取首道工序记录
     * 
     * @param sn 产品序列号
     * @return 首道工序记录
     */
    @GetMapping("/first/{sn}")
    ProductProcessDTO getFirstProcessBySn(@PathVariable("sn") String sn);

    /**
     * 根据产品序列号和工序编码获取过站记录
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 过站记录
     */
    @GetMapping("/sn/{sn}/process/{processCode}")
    ProductProcessDTO getBySnAndProcessCode(@PathVariable("sn") String sn, 
                                          @PathVariable("processCode") String processCode);

    /**
     * 更新产品过站记录
     * 
     * @param productProcessDTO 产品过站记录DTO
     * @return 是否更新成功
     */
    @PutMapping
    Boolean update(@RequestBody ProductProcessDTO productProcessDTO);

    /**
     * 根据ID删除产品过站记录
     * 
     * @param id 记录ID
     * @return 是否删除成功
     */
    @DeleteMapping("/{id}")
    Boolean deleteById(@PathVariable("id") String id);

    /**
     * 批量删除产品过站记录
     * 
     * @param ids 记录ID列表
     * @return 是否删除成功
     */
    @DeleteMapping("/batch")
    Boolean batchDelete(@RequestBody List<String> ids);

    /**
     * 分页查询产品过站记录
     * 
     * @param query 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    PageDTO<ProductProcessDTO> page(@RequestBody ProductProcessQuery query);

    /**
     * 根据查询条件获取记录列表
     * 
     * @param query 查询条件
     * @return 记录列表
     */
    @PostMapping("/list")
    List<ProductProcessDTO> list(@RequestBody ProductProcessQuery query);

    /**
     * 根据查询条件统计记录数量
     * 
     * @param query 查询条件
     * @return 记录数量
     */
    @PostMapping("/count")
    Long count(@RequestBody ProductProcessQuery query);

    /**
     * 统计指定产线的过站记录数量
     * 
     * @param lineCode 产线编码
     * @return 记录数量
     */
    @GetMapping("/count/line/{lineCode}")
    Long countByLineCode(@PathVariable("lineCode") String lineCode);

    /**
     * 统计指定时间范围内的过站记录数量
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 记录数量
     */
    @GetMapping("/count/time")
    Long countByTimeRange(@RequestParam("startTime") String startTime, 
                         @RequestParam("endTime") String endTime);

    /**
     * 统计指定测试结果的记录数量
     * 
     * @param result 测试结果
     * @return 记录数量
     */
    @GetMapping("/count/result/{result}")
    Long countByResult(@PathVariable("result") String result);

    /**
     * 检查产品是否已过指定工序
     * 
     * @param sn 产品序列号
     * @param processCode 工序编码
     * @return 是否已过工序
     */
    @GetMapping("/check/{sn}/process/{processCode}")
    Boolean hasPassedProcess(@PathVariable("sn") String sn, 
                           @PathVariable("processCode") String processCode);

    /**
     * 获取产品的过站路径
     * 
     * @param sn 产品序列号
     * @return 过站路径（工序编码列表）
     */
    @GetMapping("/path/{sn}")
    List<String> getProcessPath(@PathVariable("sn") String sn);
}
