package com.dinglite.mongodb.api.service.fallback;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.mongodb.api.domain.dto.ProductInfoDTO;
import com.dinglite.mongodb.api.domain.query.ProductInfoQuery;
import com.dinglite.mongodb.api.service.ProductInfoClient;
import feign.hystrix.FallbackFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * 产品基础信息Feign客户端降级处理工厂
 * 
 * <AUTHOR>
 * @since 2025-07-31
 */
@Slf4j
@Component
public class ProductInfoClientFallbackFactory implements FallbackFactory<ProductInfoClient> {

    @Override
    public ProductInfoClient create(Throwable cause) {
        log.error("ProductInfoClient调用失败，进入降级处理", cause);
        
        return new ProductInfoClient() {
            @Override
            public ProductInfoDTO create(ProductInfoDTO productInfoDTO) {
                log.error("创建产品基础信息失败，降级处理", cause);
                return null;
            }

            @Override
            public Integer batchCreate(List<ProductInfoDTO> productInfoDTOList) {
                log.error("批量创建产品基础信息失败，降级处理", cause);
                return 0;
            }

            @Override
            public ProductInfoDTO getById(String id) {
                log.error("根据ID获取产品基础信息失败，降级处理，ID: {}", id, cause);
                return null;
            }

            @Override
            public ProductInfoDTO getBySn(String sn) {
                log.error("根据产品序列号获取产品信息失败，降级处理，SN: {}", sn, cause);
                return null;
            }

            @Override
            public List<ProductInfoDTO> getBySnList(List<String> snList) {
                log.error("根据产品序列号列表获取产品信息失败，降级处理", cause);
                return new ArrayList<>();
            }

            @Override
            public Boolean update(ProductInfoDTO productInfoDTO) {
                log.error("更新产品基础信息失败，降级处理", cause);
                return false;
            }

            @Override
            public Boolean deleteById(String id) {
                log.error("删除产品基础信息失败，降级处理，ID: {}", id, cause);
                return false;
            }

            @Override
            public Boolean batchDelete(List<String> ids) {
                log.error("批量删除产品基础信息失败，降级处理", cause);
                return false;
            }

            @Override
            public PageDTO<ProductInfoDTO> page(ProductInfoQuery query) {
                log.error("分页查询产品基础信息失败，降级处理", cause);
                return new PageDTO<>();
            }

            @Override
            public List<ProductInfoDTO> list(ProductInfoQuery query) {
                log.error("查询产品基础信息列表失败，降级处理", cause);
                return new ArrayList<>();
            }

            @Override
            public Long count(ProductInfoQuery query) {
                log.error("统计产品基础信息数量失败，降级处理", cause);
                return 0L;
            }

            @Override
            public Long countByLineCode(String lineCode) {
                log.error("根据产线编码统计产品数量失败，降级处理，LineCode: {}", lineCode, cause);
                return 0L;
            }

            @Override
            public Long countByStatus(String status) {
                log.error("根据状态统计产品数量失败，降级处理，Status: {}", status, cause);
                return 0L;
            }

            @Override
            public Long countByTimeRange(String startTime, String endTime) {
                log.error("根据时间范围统计产品数量失败，降级处理", cause);
                return 0L;
            }

            @Override
            public Boolean existsBySn(String sn) {
                log.error("检查产品序列号是否存在失败，降级处理，SN: {}", sn, cause);
                return false;
            }

            @Override
            public List<ProductInfoDTO> getByPackageCode(String packageCode) {
                log.error("根据包装编码获取产品信息失败，降级处理，PackageCode: {}", packageCode, cause);
                return new ArrayList<>();
            }

            @Override
            public List<ProductInfoDTO> getByInnerBoxCode(String innerBoxCode) {
                log.error("根据内箱编码获取产品信息失败，降级处理，InnerBoxCode: {}", innerBoxCode, cause);
                return new ArrayList<>();
            }

            @Override
            public List<ProductInfoDTO> getByOuterBoxCode(String outerBoxCode) {
                log.error("根据外箱编码获取产品信息失败，降级处理，OuterBoxCode: {}", outerBoxCode, cause);
                return new ArrayList<>();
            }

            @Override
            public List<ProductInfoDTO> getByPalletCode(String palletCode) {
                log.error("根据栈板编码获取产品信息失败，降级处理，PalletCode: {}", palletCode, cause);
                return new ArrayList<>();
            }

            @Override
            public Boolean updateStatus(String sn, String status) {
                log.error("更新产品状态失败，降级处理，SN: {}, Status: {}", sn, status, cause);
                return false;
            }

            @Override
            public Boolean updatePackageStatus(String sn, String packageStatus) {
                log.error("更新产品包装状态失败，降级处理，SN: {}, PackageStatus: {}", sn, packageStatus, cause);
                return false;
            }

            @Override
            public Boolean updateLastProcess(String sn, String lastProcess, String lastStation) {
                log.error("更新产品最后工序信息失败，降级处理，SN: {}", sn, cause);
                return false;
            }

            @Override
            public Boolean addCompletedProcess(String sn, String processCode) {
                log.error("添加已完成工序失败，降级处理，SN: {}, ProcessCode: {}", sn, processCode, cause);
                return false;
            }
        };
    }
}
