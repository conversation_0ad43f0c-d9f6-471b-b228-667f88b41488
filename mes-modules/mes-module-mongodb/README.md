# MES MongoDB模块

## 概述

MES MongoDB模块是MES系统中负责MongoDB数据库集成的微服务模块，主要用于处理生产过程中的大数据存储和查询需求。

## 功能特性

### 核心功能
- **产品过站记录管理**: 记录产品在生产线上的过站信息
- **产品基础信息管理**: 管理产品的基本信息和状态
- **产品异常信息管理**: 记录和处理产品异常情况
- **产品物料追溯**: 追溯产品使用的物料信息
- **包装信息管理**: 管理产品包装相关信息

### 技术特性
- **高性能读写**: 优化的MongoDB查询和索引策略
- **分页查询**: 支持大数据量的分页查询
- **复杂查询**: 支持多条件组合查询和聚合查询
- **数据统计**: 提供各种维度的数据统计功能
- **微服务集成**: 通过Feign客户端支持微服务间调用

## 技术栈

- **Spring Boot**: 2.1.6.RELEASE
- **Spring Data MongoDB**: 2.1.9.RELEASE
- **MongoDB Java Driver**: 3.12.11
- **Spring Cloud**: Greenwich.SR2
- **Java**: 1.8

## 模块结构

```
mes-module-mongodb/
├── mes-module-mongodb-api/          # API接口模块
│   ├── domain/
│   │   ├── dto/                     # 数据传输对象
│   │   ├── entity/                  # MongoDB实体类
│   │   └── query/                   # 查询条件对象
│   ├── service/                     # Feign客户端接口
│   └── constant/                    # 常量定义
└── mes-module-mongodb-service/      # 服务实现模块
    ├── controller/                  # REST API控制器
    ├── service/                     # 业务逻辑层
    ├── repository/                  # 数据访问层
    ├── config/                      # 配置类
    └── test/                        # 测试代码
```

## 数据模型

### 1. ProductProcess (产品过站记录)
- 记录产品在生产线上的过站信息
- 包含测试结果、设备参数、操作员信息等
- 支持按产线、工序、时间等维度查询

### 2. ProductInfo (产品基础信息)
- 存储产品的基本信息和当前状态
- 包含型号、订单、产线、包装状态等信息
- 支持产品生命周期跟踪

### 3. ProductException (产品异常信息)
- 记录产品的异常情况和处理流程
- 包含申报、复判、返修等环节信息
- 支持异常流程跟踪

### 4. ProductMaterial (产品物料追溯)
- 记录产品使用的物料信息
- 支持物料批次追溯和消耗统计
- 包含供应商信息和绑定关系

### 5. PackageInfo (包装信息)
- 管理产品包装相关信息
- 支持多层级包装结构
- 包含包装容量和状态管理

## 配置说明

### MongoDB连接配置
```properties
# MongoDB基础配置
spring.data.mongodb.host=127.0.0.1
spring.data.mongodb.port=27017
spring.data.mongodb.database=mes_mongodb

# 连接池配置
spring.data.mongodb.option.connections-per-host=100
spring.data.mongodb.option.min-connections-per-host=10
spring.data.mongodb.option.max-wait-time=120000
spring.data.mongodb.option.connect-timeout=10000
```

### 应用配置
```properties
# 服务配置
server.port=8010
spring.application.name=mes-mongodb

# 日志配置
logging.level.com.dinglite=debug
```

## API接口

### 产品过站记录API
- `POST /mongodb/product-process` - 创建过站记录
- `GET /mongodb/product-process/{id}` - 根据ID获取记录
- `GET /mongodb/product-process/sn/{sn}` - 根据SN获取记录列表
- `GET /mongodb/product-process/latest/{sn}` - 获取最新过站记录
- `POST /mongodb/product-process/page` - 分页查询
- `GET /mongodb/product-process/path/{sn}` - 获取过站路径

### 产品基础信息API
- `POST /mongodb/product-info` - 创建产品信息
- `GET /mongodb/product-info/sn/{sn}` - 根据SN获取产品信息
- `PUT /mongodb/product-info/status/{sn}` - 更新产品状态
- `POST /mongodb/product-info/page` - 分页查询

### 健康检查API
- `GET /mongodb/health/check` - 检查MongoDB连接状态
- `GET /mongodb/health/info` - 获取数据库信息
- `GET /mongodb/health/ping` - 测试连接

## 使用示例

### 1. 创建过站记录
```java
@Autowired
private ProductProcessClient productProcessClient;

ProductProcessDTO dto = new ProductProcessDTO();
dto.setResult("PASS");
dto.setProcessTime(LocalDateTime.now());
// ... 设置其他字段

ProductProcessDTO result = productProcessClient.create(dto);
```

### 2. 查询产品信息
```java
@Autowired
private ProductInfoClient productInfoClient;

ProductInfoDTO productInfo = productInfoClient.getBySn("PRODUCT_SN_001");
```

### 3. 分页查询
```java
ProductProcessQuery query = new ProductProcessQuery();
query.setCurrent(1);
query.setSize(10);
query.setLineCode("LINE001");
query.setResult("PASS");

PageDTO<ProductProcessDTO> page = productProcessClient.page(query);
```

## 测试

### 运行单元测试
```bash
mvn test
```

### 运行集成测试
```bash
mvn test -Dtest=MongodbIntegrationTest
```

### 测试覆盖率
项目包含完整的单元测试和集成测试，覆盖主要业务逻辑和API接口。

## 部署

### 1. 构建项目
```bash
mvn clean package
```

### 2. 启动服务
```bash
java -jar mes-module-mongodb-service/target/mes-mongodb.jar
```

### 3. Docker部署
```bash
docker build -t mes-mongodb .
docker run -p 8010:8010 mes-mongodb
```

## 监控

### 健康检查
- 应用健康状态: `http://localhost:8010/actuator/health`
- MongoDB连接状态: `http://localhost:8010/mongodb/health/check`

### 性能监控
- 应用指标: `http://localhost:8010/actuator/metrics`
- 数据库统计: `http://localhost:8010/mongodb/health/info`

## 注意事项

1. **数据库版本**: 确保MongoDB版本为8.0.10或兼容版本
2. **索引优化**: 根据查询模式创建合适的索引
3. **连接池配置**: 根据并发量调整连接池参数
4. **数据备份**: 定期备份重要数据
5. **性能监控**: 监控查询性能和资源使用情况

## 故障排除

### 常见问题
1. **连接超时**: 检查MongoDB服务状态和网络连接
2. **查询慢**: 检查索引配置和查询条件
3. **内存不足**: 调整JVM参数和连接池配置
4. **数据不一致**: 检查事务配置和并发控制

### 日志分析
- 应用日志: `/logs/mes-mongodb.log`
- MongoDB日志: 查看MongoDB服务日志
- 错误日志: 关注ERROR和WARN级别日志

## 版本历史

- **v1.0.0**: 初始版本，支持基础CRUD操作
- **v1.1.0**: 增加复杂查询和统计功能
- **v1.2.0**: 优化性能和添加监控功能

## 联系方式

如有问题或建议，请联系开发团队。
