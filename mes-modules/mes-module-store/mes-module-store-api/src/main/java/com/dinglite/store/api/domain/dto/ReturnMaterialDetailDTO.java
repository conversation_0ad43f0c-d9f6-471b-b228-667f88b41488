package com.dinglite.store.api.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description 退料入库明细DTO
 * <AUTHOR> @Date 2025-07-28
 */
@Data
@Accessors(chain = true)
public class ReturnMaterialDetailDTO {

    /**
     * 箱数
     */
    private Integer boxNum;

    /**
     * 件数
     */
    private Integer pieceNum;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物料代码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;
}
