package com.dinglite.store.api.domain.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Description 退料入库DTO
 * <AUTHOR> @Date 2025-07-28
 */
@Data
@Accessors(chain = true)
public class ReturnMaterialDTO {

    /**
     * 单据类型（3.退料入库单）
     */
    private String documentType;

    /**
     * 入库方式（0.单箱入库）
     */
    private String enterType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 入库单状态（1.待收货）
     */
    private Integer status;

    /**
     * 仓库ID
     */
    private Long storeId;

    /**
     * 仓库名称
     */
    private String storeName;

    /**
     * 入库明细列表
     */
    private List<ReturnMaterialDetailDTO> list;
}
