package com.dinglite.product.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dinglite.product.api.domain.entity.ProductOrderLossDetail;

import java.math.BigDecimal;
import java.util.List;

/**
 * <p>
 * 生产工单损耗明细表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IProductOrderLossDetailService extends IService<ProductOrderLossDetail> {

    /**
     * 根据损耗单ID查询明细列表
     *
     * @param productOrderLossId 损耗单ID
     * @return 明细列表
     */
    List<ProductOrderLossDetail> listByLossId(Long productOrderLossId);

    /**
     * 根据物料ID查询损耗明细列表
     *
     * @param materialId 物料ID
     * @return 明细列表
     */
    List<ProductOrderLossDetail> listByMaterialId(Long materialId);

    /**
     * 批量保存损耗明细
     *
     * @param productOrderLossId 损耗单ID
     * @param details            明细列表
     * @return 是否成功
     */
    boolean batchSave(Long productOrderLossId, List<ProductOrderLossDetail> details);

    /**
     * 根据损耗单ID删除所有明细
     *
     * @param productOrderLossId 损耗单ID
     * @return 是否成功
     */
    boolean deleteByLossId(Long productOrderLossId);

    /**
     * 统计指定物料的损耗总数量
     *
     * @param materialId 物料ID
     * @return 损耗总数量
     */
    BigDecimal sumLossNumByMaterialId(Long materialId);

    /**
     * 查询指定时间范围内的损耗明细
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 明细列表
     */
    List<ProductOrderLossDetail> listByTimeRange(String startTime, String endTime);

    /**
     * 根据生产工单ID查询所有相关的损耗明细
     *
     * @param productOrderId 生产工单ID
     * @return 明细列表
     */
    List<ProductOrderLossDetail> listByProductOrderId(Long productOrderId);

    /**
     * 验证损耗明细数据
     *
     * @param detail 损耗明细
     * @return 是否有效
     */
    boolean validateDetail(ProductOrderLossDetail detail);
}
