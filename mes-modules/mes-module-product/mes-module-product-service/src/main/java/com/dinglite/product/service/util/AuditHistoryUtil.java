package com.dinglite.product.service.util;

import com.dinglite.product.api.domain.constant.ProductOrderLossConstant;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 审核历史记录工具类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
public class AuditHistoryUtil {

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 添加审核记录
     *
     * @param currentCheckRemark 当前审核备注
     * @param auditor 审核人
     * @param fromStatus 原状态
     * @param toStatus 目标状态
     * @param reason 审核原因
     * @return 更新后的审核备注
     */
    public static String addAuditRecord(String currentCheckRemark, String auditor,
                                       Integer fromStatus, Integer toStatus, String reason) {
        StringBuilder sb = new StringBuilder();

        // 保留原有的审核历史
        if (StringUtils.hasText(currentCheckRemark)) {
            sb.append(currentCheckRemark).append("\n");
        }

        // 添加新的审核记录 - 使用简洁格式：时间#操作人#状态#备注
        String timestamp = LocalDateTime.now().format(FORMATTER);
        String statusName = getStatusName(toStatus);

        // 处理审核人为空或"system"的情况
        String finalAuditor = auditor;
        if (!StringUtils.hasText(auditor) || "system".equals(auditor)) {
            log.warn("审核人为空或为system，当前审核人: {}, 目标状态: {}", auditor, toStatus);
            finalAuditor = StringUtils.hasText(auditor) ? auditor : "system";
        }

        // 处理审核原因为空的情况
        String finalReason = StringUtils.hasText(reason) ? reason : "";

        sb.append(timestamp).append("#")
          .append(finalAuditor).append("#")
          .append(statusName).append("#")
          .append(finalReason);

        return sb.toString();
    }

    /**
     * 获取状态名称
     */
    private static String getStatusName(Integer status) {
        if (ProductOrderLossConstant.Status.PENDING.equals(status)) {
            return "待审核";
        } else if (ProductOrderLossConstant.Status.APPROVED.equals(status)) {
            return "审核通过";
        } else if (ProductOrderLossConstant.Status.REJECTED.equals(status)) {
            return "审核未通过";
        }
        return "未知状态";
    }

    /**
     * 获取最新的审核原因（用于显示）
     * 适配新格式：时间#操作人#状态#备注
     */
    public static String getLatestAuditReason(String checkRemark) {
        if (!StringUtils.hasText(checkRemark)) {
            return "";
        }

        String[] lines = checkRemark.split("\n");
        if (lines.length == 0) {
            return "";
        }

        // 返回最后一行的审核原因
        String lastLine = lines[lines.length - 1];

        // 检查是否是新格式（包含#分隔符）
        if (lastLine.contains("#")) {
            String[] parts = lastLine.split("#");
            if (parts.length >= 4) {
                // 新格式：时间#操作人#状态#备注
                return parts[3]; // 返回备注部分
            }
        } else {
            // 兼容旧格式：[时间] 操作人 动作 -> 状态: 备注
            int colonIndex = lastLine.lastIndexOf(": ");
            if (colonIndex > 0 && colonIndex < lastLine.length() - 2) {
                return lastLine.substring(colonIndex + 2);
            }
        }

        return lastLine;
    }
}
