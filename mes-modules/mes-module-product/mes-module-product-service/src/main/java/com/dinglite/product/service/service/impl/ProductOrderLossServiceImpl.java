package com.dinglite.product.service.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dinglite.product.api.domain.constant.ProductOrderLossConstant;
import com.dinglite.product.api.domain.entity.ProductLineMaterial;
import com.dinglite.product.api.domain.entity.ProductOrderLoss;
import com.dinglite.product.api.domain.entity.ProductOrderLossDetail;
import com.dinglite.product.service.mapper.ProductOrderLossMapper;
import com.dinglite.product.service.service.IProductOrderLossDetailService;
import com.dinglite.product.service.service.IProductOrderLossService;
import com.dinglite.product.service.service.ProductLineMaterialService;
import com.dinglite.product.service.util.AuditHistoryUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 生产工单损耗单主表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@Service
public class ProductOrderLossServiceImpl extends ServiceImpl<ProductOrderLossMapper, ProductOrderLoss>
        implements IProductOrderLossService {

    @Autowired
    private IProductOrderLossDetailService productOrderLossDetailService;

    @Autowired
    private ProductLineMaterialService productLineMaterialService;

    @Override
    public List<ProductOrderLoss> listByProductOrderId(Long productOrderId) {
        return this.list(Wrappers.<ProductOrderLoss>lambdaQuery()
                .eq(ProductOrderLoss::getProductOrderId, productOrderId));
    }

    @Override
    public List<ProductOrderLoss> listByProductLineId(Long productLineId) {
        return this.list(Wrappers.<ProductOrderLoss>lambdaQuery()
                .eq(ProductOrderLoss::getProductLineId, productLineId));
    }

    @Override
    public List<ProductOrderLoss> listByStatus(Integer status) {
        return this.list(Wrappers.<ProductOrderLoss>lambdaQuery()
                .eq(ProductOrderLoss::getStatus, status));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approve(Long productOrderLossId, Integer status, String checkBy, String checkRemark) {
        ProductOrderLoss loss = getById(productOrderLossId);
        if (loss == null) {
            log.error("损耗单不存在，ID: {}", productOrderLossId);
            return false;
        }

        // 检查当前状态是否为待审核
        if (!ProductOrderLossConstant.Status.PENDING.equals(loss.getStatus())) {
            log.error("损耗单状态不是待审核，无法审核，当前状态: {}", loss.getStatus());
            return false;
        }

        // 记录原状态
        Integer fromStatus = loss.getStatus();

        // 如果审核通过，需要扣减产线结转数量
        if (ProductOrderLossConstant.Status.APPROVED.equals(status)) {
            if (!deductCarryOverQuantity(loss, checkBy)) {
                log.error("扣减产线结转数量失败，损耗单ID: {}", productOrderLossId);
                return false;
            }
        }

        // 添加审核历史记录
        String updatedCheckRemark = AuditHistoryUtil.addAuditRecord(
            loss.getCheckRemark(), checkBy, fromStatus, status, checkRemark);

        // 更新审核信息
        loss.setStatus(status)
            .setCheckBy(checkBy)
            .setCheckTime(LocalDateTime.now())
            .setCheckRemark(updatedCheckRemark)
            .setUpdateBy(checkBy)
            .setUpdateTime(LocalDateTime.now());

        return updateById(loss);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchApprove(List<Long> ids, Integer status, String checkBy, String checkRemark) {
        if (ids == null || ids.isEmpty()) {
            return false;
        }

        for (Long id : ids) {
            if (!approve(id, status, checkBy, checkRemark)) {
                log.error("批量审核失败，损耗单ID: {}", id);
                return false;
            }
        }
        return true;
    }

    /**
     * 扣减产线结转数量
     *
     * @param loss 损耗单
     * @param operator 操作人
     * @return 是否成功
     */
    private boolean deductCarryOverQuantity(ProductOrderLoss loss, String operator) {
        try {
            // 获取损耗明细列表
            List<ProductOrderLossDetail> details = productOrderLossDetailService.listByLossId(loss.getProductOrderLossId());

            for (ProductOrderLossDetail detail : details) {
                // 查询对应的产线物料记录
                ProductLineMaterial lineMaterial = productLineMaterialService.getOne(
                    Wrappers.<ProductLineMaterial>lambdaQuery()
                        .eq(ProductLineMaterial::getProductLineId, loss.getProductLineId())
                        .eq(ProductLineMaterial::getMaterialId, detail.getMaterialId())
                );

                if (lineMaterial == null) {
                    log.warn("未找到产线物料记录，产线ID: {}, 物料ID: {}",
                        loss.getProductLineId(), detail.getMaterialId());
                    continue;
                }

                // 计算扣减后的数量
                Integer currentQuantity = lineMaterial.getNum();
                Integer lossQuantity = detail.getLossNum().intValue();
                Integer newQuantity = currentQuantity - lossQuantity;

                // 检查结转数量是否足够，不允许负数
                if (newQuantity < 0) {
                    String errorMsg = String.format("产线结转数量不足，无法完成审核。产线: %s, 物料: %s(%s), 当前结转数量: %d, 损耗数量: %d, 缺少: %d",
                        loss.getProductLineName(),
                        lineMaterial.getMaterialName(),
                        lineMaterial.getMaterialCode(),
                        currentQuantity,
                        lossQuantity,
                        Math.abs(newQuantity));
                    log.error(errorMsg);
                    throw new RuntimeException(errorMsg);
                }

                // 更新产线物料数量
                lineMaterial.setNum(newQuantity);
                lineMaterial.setUpdateBy(operator); // 使用实际操作人
                lineMaterial.setUpdateTime(LocalDateTime.now());

                boolean updateResult = productLineMaterialService.updateById(lineMaterial);
                if (!updateResult) {
                    log.error("更新产线物料数量失败，产线ID: {}, 物料ID: {}",
                        loss.getProductLineId(), detail.getMaterialId());
                    return false;
                }

                log.info("成功扣减产线结转数量，产线ID: {}, 物料ID: {}, 原数量: {}, 损耗数量: {}, 新数量: {}",
                    loss.getProductLineId(), detail.getMaterialId(), currentQuantity, lossQuantity, newQuantity);
            }

            return true;
        } catch (Exception e) {
            log.error("扣减产线结转数量时发生异常，损耗单ID: {}", loss.getProductOrderLossId(), e);
            return false;
        }
    }

    @Override
    public int countByProductOrderId(Long productOrderId) {
        return Math.toIntExact(this.count(Wrappers.<ProductOrderLoss>lambdaQuery()
                .eq(ProductOrderLoss::getProductOrderId, productOrderId)));
    }

    @Override
    public int countByProductLineId(Long productLineId) {
        return Math.toIntExact(this.count(Wrappers.<ProductOrderLoss>lambdaQuery()
                .eq(ProductOrderLoss::getProductLineId, productLineId)));
    }

    @Override
    public List<ProductOrderLoss> listByTimeRange(String startTime, String endTime) {
        return this.list(Wrappers.<ProductOrderLoss>lambdaQuery()
                .ge(ProductOrderLoss::getCreateTime, startTime)
                .le(ProductOrderLoss::getCreateTime, endTime));
    }

    @Override
    public boolean canModify(Long productOrderLossId) {
        ProductOrderLoss loss = getById(productOrderLossId);
        if (loss == null) {
            return false;
        }
        // 待审核和审核未通过状态的损耗单可以修改
        // 审核通过的损耗单不允许修改（已确认的数据不应再变更）
        return ProductOrderLossConstant.Status.PENDING.equals(loss.getStatus())
            || ProductOrderLossConstant.Status.REJECTED.equals(loss.getStatus());
    }

    @Override
    public boolean canDelete(Long productOrderLossId) {
        ProductOrderLoss loss = getById(productOrderLossId);
        if (loss == null) {
            return false;
        }
        // 待审核和审核未通过状态的损耗单可以删除
        // 审核通过的损耗单不允许删除（已确认的数据不应删除）
        return ProductOrderLossConstant.Status.PENDING.equals(loss.getStatus())
            || ProductOrderLossConstant.Status.REJECTED.equals(loss.getStatus());
    }


}
