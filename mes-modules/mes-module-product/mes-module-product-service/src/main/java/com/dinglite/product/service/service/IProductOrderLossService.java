package com.dinglite.product.service.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dinglite.product.api.domain.entity.ProductOrderLoss;

import java.util.List;

/**
 * <p>
 * 生产工单损耗单主表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public interface IProductOrderLossService extends IService<ProductOrderLoss> {

    /**
     * 根据生产工单ID查询损耗单列表
     *
     * @param productOrderId 生产工单ID
     * @return 损耗单列表
     */
    List<ProductOrderLoss> listByProductOrderId(Long productOrderId);

    /**
     * 根据产线ID查询损耗单列表
     *
     * @param productLineId 产线ID
     * @return 损耗单列表
     */
    List<ProductOrderLoss> listByProductLineId(Long productLineId);

    /**
     * 根据审核状态查询损耗单列表
     *
     * @param status 审核状态
     * @return 损耗单列表
     */
    List<ProductOrderLoss> listByStatus(Integer status);

    /**
     * 审核损耗单
     *
     * @param productOrderLossId 损耗单ID
     * @param status             审核状态（1.审核通过 2.审核未通过）
     * @param checkBy            审核人
     * @param checkRemark        审核备注
     * @return 是否成功
     */
    boolean approve(Long productOrderLossId, Integer status, String checkBy, String checkRemark);

    /**
     * 批量审核损耗单
     *
     * @param ids         损耗单ID列表
     * @param status      审核状态
     * @param checkBy     审核人
     * @param checkRemark 审核备注
     * @return 是否成功
     */
    boolean batchApprove(List<Long> ids, Integer status, String checkBy, String checkRemark);

    /**
     * 统计指定生产工单的损耗单数量
     *
     * @param productOrderId 生产工单ID
     * @return 损耗单数量
     */
    int countByProductOrderId(Long productOrderId);

    /**
     * 统计指定产线的损耗单数量
     *
     * @param productLineId 产线ID
     * @return 损耗单数量
     */
    int countByProductLineId(Long productLineId);

    /**
     * 查询指定时间范围内的损耗单
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 损耗单列表
     */
    List<ProductOrderLoss> listByTimeRange(String startTime, String endTime);

    /**
     * 检查损耗单是否可以修改
     *
     * 业务规则：
     * - 待审核状态(0)：可以修改
     * - 审核未通过状态(2)：可以修改（允许用户根据审核意见调整）
     * - 审核通过状态(1)：不可修改（已确认的数据不应变更）
     *
     * @param productOrderLossId 损耗单ID
     * @return 是否可以修改
     */
    boolean canModify(Long productOrderLossId);

    /**
     * 检查损耗单是否可以删除
     *
     * 业务规则：
     * - 待审核状态(0)：可以删除
     * - 审核未通过状态(2)：可以删除（允许用户删除后重新创建）
     * - 审核通过状态(1)：不可删除（已确认的数据不应删除）
     *
     * @param productOrderLossId 损耗单ID
     * @return 是否可以删除
     */
    boolean canDelete(Long productOrderLossId);


}
