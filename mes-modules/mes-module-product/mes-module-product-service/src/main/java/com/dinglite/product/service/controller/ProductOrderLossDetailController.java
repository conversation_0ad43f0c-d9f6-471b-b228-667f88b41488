package com.dinglite.product.service.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dinglite.common.annotation.LoadPayload;
import com.dinglite.common.domain.PageDTO;
import com.dinglite.mybatis.utils.PageUtils;
import com.dinglite.product.api.domain.constant.ProductOrderLossConstant;
import com.dinglite.product.api.domain.dto.ProductOrderLossDetailDTO;
import com.dinglite.product.api.domain.entity.ProductOrderLossDetail;
import com.dinglite.product.api.domain.query.ProductOrderLossDetailQuery;
import com.dinglite.product.service.service.IProductOrderLossDetailService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 生产工单损耗明细表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Slf4j
@RestController
@RequestMapping("/product/product-order-loss-detail/api")
@Api(tags = "生产工单损耗明细API")
public class ProductOrderLossDetailController {

    @Autowired
    private IProductOrderLossDetailService productOrderLossDetailService;

    /**
     * 转换为DTO
     */
    public static ProductOrderLossDetailDTO toDTO(ProductOrderLossDetail detail) {
        if (detail == null) {
            return null;
        }
        ProductOrderLossDetailDTO dto = new ProductOrderLossDetailDTO();
        BeanUtils.copyProperties(detail, dto);
        return dto;
    }

    @GetMapping("/getInfo")
    @ApiOperation("获取生产工单损耗明细详情")
    public ProductOrderLossDetailDTO getInfo(@RequestParam Long productOrderLossDetailId) {
        ProductOrderLossDetail detail = productOrderLossDetailService.getById(productOrderLossDetailId);
        return toDTO(detail);
    }

    @PostMapping("/page")
    @ApiOperation("分页查询生产工单损耗明细")
    @LoadPayload
    public PageDTO<ProductOrderLossDetailDTO> page(@RequestBody ProductOrderLossDetailQuery query) {
        LambdaQueryWrapper<ProductOrderLossDetail> queryWrapper = buildQueryWrapper(query);
        
        IPage<ProductOrderLossDetail> page = productOrderLossDetailService.page(
                new Page<>(query.getCurrent(), query.getSize()), queryWrapper);
        
        return PageUtils.toDTO(page.convert(ProductOrderLossDetailController::toDTO));
    }

    @PostMapping("/list")
    @ApiOperation("查询生产工单损耗明细列表")
    public List<ProductOrderLossDetailDTO> list(@RequestBody ProductOrderLossDetailQuery query) {
        LambdaQueryWrapper<ProductOrderLossDetail> queryWrapper = buildQueryWrapper(query);
        
        return productOrderLossDetailService.list(queryWrapper)
                .stream()
                .map(ProductOrderLossDetailController::toDTO)
                .collect(Collectors.toList());
    }

    @GetMapping("/by-loss-id")
    @ApiOperation("根据损耗单ID查询明细列表")
    public List<ProductOrderLossDetailDTO> listByLossId(@RequestParam Long productOrderLossId) {
        List<ProductOrderLossDetail> details = productOrderLossDetailService.listByLossId(productOrderLossId);
        return details.stream()
                .map(ProductOrderLossDetailController::toDTO)
                .collect(Collectors.toList());
    }

    @GetMapping("/by-material-id")
    @ApiOperation("根据物料ID查询损耗明细列表")
    public List<ProductOrderLossDetailDTO> listByMaterialId(@RequestParam Long materialId) {
        List<ProductOrderLossDetail> details = productOrderLossDetailService.listByMaterialId(materialId);
        return details.stream()
                .map(ProductOrderLossDetailController::toDTO)
                .collect(Collectors.toList());
    }

    @GetMapping("/by-product-order-id")
    @ApiOperation("根据生产工单ID查询损耗明细列表")
    public List<ProductOrderLossDetailDTO> listByProductOrderId(@RequestParam Long productOrderId) {
        List<ProductOrderLossDetail> details = productOrderLossDetailService.listByProductOrderId(productOrderId);
        return details.stream()
                .map(ProductOrderLossDetailController::toDTO)
                .collect(Collectors.toList());
    }

    @GetMapping("/sum-loss-num")
    @ApiOperation("统计指定物料的损耗总数量")
    public BigDecimal sumLossNumByMaterialId(@RequestParam Long materialId) {
        return productOrderLossDetailService.sumLossNumByMaterialId(materialId);
    }

    @GetMapping("/time-range")
    @ApiOperation("查询指定时间范围内的损耗明细")
    public List<ProductOrderLossDetailDTO> listByTimeRange(@RequestParam String startTime,
                                                          @RequestParam String endTime) {
        List<ProductOrderLossDetail> details = productOrderLossDetailService.listByTimeRange(startTime, endTime);
        return details.stream()
                .map(ProductOrderLossDetailController::toDTO)
                .collect(Collectors.toList());
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<ProductOrderLossDetail> buildQueryWrapper(ProductOrderLossDetailQuery query) {
        LambdaQueryWrapper<ProductOrderLossDetail> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(ProductOrderLossDetail::getFlag, ProductOrderLossConstant.Flag.NOT_DELETED)
                .eq(query.getProductOrderLossDetailId() != null, ProductOrderLossDetail::getProductOrderLossDetailId, query.getProductOrderLossDetailId())
                .eq(query.getProductOrderLossId() != null, ProductOrderLossDetail::getProductOrderLossId, query.getProductOrderLossId())
                .eq(query.getMaterialId() != null, ProductOrderLossDetail::getMaterialId, query.getMaterialId())
                .like(StringUtils.hasText(query.getMaterialCode()), ProductOrderLossDetail::getMaterialCode, query.getMaterialCode())
                .like(StringUtils.hasText(query.getMaterialName()), ProductOrderLossDetail::getMaterialName, query.getMaterialName())
                .eq(StringUtils.hasText(query.getCreateBy()), ProductOrderLossDetail::getCreateBy, query.getCreateBy())
                .orderByDesc(ProductOrderLossDetail::getCreateTime);
        
        return queryWrapper;
    }
}
