package com.dinglite.product.api.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 审核历史记录DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ApiModel(value = "AuditHistoryDTO", description = "审核历史记录")
public class AuditHistoryDTO {

    @ApiModelProperty(value = "审核ID")
    private String auditId;

    @ApiModelProperty(value = "审核时间")
    private LocalDateTime auditTime;

    @ApiModelProperty(value = "审核人")
    private String auditor;

    @ApiModelProperty(value = "原状态")
    private Integer fromStatus;

    @ApiModelProperty(value = "目标状态")
    private Integer toStatus;

    @ApiModelProperty(value = "状态名称")
    private String statusName;

    @ApiModelProperty(value = "审核原因")
    private String reason;

    @ApiModelProperty(value = "审核动作")
    private String action;
}
