package com.dinglite.product.api.domain.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <p>
 * 生产工单损耗明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "ProductOrderLossDetail对象", description = "生产工单损耗明细表")
public class ProductOrderLossDetail extends Model<ProductOrderLossDetail> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "生产工单损耗明细ID")
    @TableId(value = "product_order_loss_detail_id", type = IdType.AUTO)
    private Long productOrderLossDetailId;

    @ApiModelProperty(value = "生产工单损耗单ID")
    private Long productOrderLossId;

    @ApiModelProperty(value = "物料ID")
    private Long materialId;

    @ApiModelProperty(value = "物料编码")
    private String materialCode;

    @ApiModelProperty(value = "物料名称")
    private String materialName;

    @ApiModelProperty(value = "物料规格")
    private String materialSpec;

    @ApiModelProperty(value = "单位")
    private String unit;

    @ApiModelProperty(value = "损耗数量")
    private BigDecimal lossNum;

    @ApiModelProperty(value = "备注")
    private String remark;

    @ApiModelProperty(value = "创建人")
    private String createBy;

    @ApiModelProperty(value = "创建时间")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "修改人")
    private String updateBy;

    @ApiModelProperty(value = "修改时间")
    private LocalDateTime updateTime;

    @ApiModelProperty(value = "逻辑删除（Y是 N否）")
    private String flag;

    @Override
    protected Serializable pkVal() {
        return this.productOrderLossDetailId;
    }
}
