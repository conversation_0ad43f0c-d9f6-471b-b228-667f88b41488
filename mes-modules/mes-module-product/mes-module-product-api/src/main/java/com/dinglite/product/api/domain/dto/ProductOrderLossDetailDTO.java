package com.dinglite.product.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 生产工单损耗明细DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Accessors(chain = true)
public class ProductOrderLossDetailDTO {

    /**
     * 生产工单损耗明细ID
     */
    private Long productOrderLossDetailId;

    /**
     * 生产工单损耗单ID
     */
    private Long productOrderLossId;

    /**
     * 物料ID
     */
    @NotNull(message = "物料ID不能为空")
    private Long materialId;

    /**
     * 物料编码
     */
    @NotNull(message = "物料编码不能为空")
    private String materialCode;

    /**
     * 物料名称
     */
    @NotNull(message = "物料名称不能为空")
    private String materialName;

    /**
     * 物料规格
     */
    private String materialSpec;

    /**
     * 单位
     */
    private String unit;

    /**
     * 损耗数量
     */
    @NotNull(message = "损耗数量不能为空")
    private BigDecimal lossNum;

    /**
     * 备注
     */
    private String remark;

    /**
     * 产线在工数量
     */
    private Integer generateNum;

    /**
     * 结转预定量
     */
    private Integer generateReserveNum;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;
}
