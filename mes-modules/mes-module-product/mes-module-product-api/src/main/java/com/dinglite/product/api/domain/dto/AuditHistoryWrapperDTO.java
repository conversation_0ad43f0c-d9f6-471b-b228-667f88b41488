package com.dinglite.product.api.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审核历史记录包装DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@ApiModel(value = "AuditHistoryWrapperDTO", description = "审核历史记录包装")
public class AuditHistoryWrapperDTO {

    @ApiModelProperty(value = "审核历史列表")
    private List<AuditHistoryDTO> auditHistory;

    @ApiModelProperty(value = "当前状态")
    private Integer currentStatus;

    @ApiModelProperty(value = "最后审核时间")
    private LocalDateTime lastAuditTime;

    @ApiModelProperty(value = "最后审核人")
    private String lastAuditor;
}
