package com.dinglite.product.api.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.datatype.jsr310.ser.LocalDateTimeSerializer;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 生产工单损耗单DTO
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Accessors(chain = true)
public class ProductOrderLossDTO {

    /**
     * 生产工单损耗单ID
     */
    private Long productOrderLossId;

    /**
     * 生产工单损耗单流水号
     */
    private String productOrderLossCode;

    /**
     * 产线ID
     */
    @NotNull(message = "产线ID不能为空")
    private Long productLineId;

    /**
     * 产线名称
     */
    @NotNull(message = "产线名称不能为空")
    private String productLineName;

    /**
     * 生产工单ID
     */
    @NotNull(message = "生产工单ID不能为空")
    private Long productOrderId;

    /**
     * 生产工单编码
     */
    @NotNull(message = "生产工单编码不能为空")
    private String productOrderCode;

    /**
     * 生产工单名称
     */
    @NotNull(message = "生产工单名称不能为空")
    private String productOrderName;

    /**
     * 审核状态（0.待审核 1.审核通过 2.审核未通过）
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    /**
     * 修改人
     */
    private String updateBy;

    /**
     * 修改时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    /**
     * 审核人
     */
    private String checkBy;

    /**
     * 审核时间
     */
    @JsonSerialize(using = LocalDateTimeSerializer.class)
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime checkTime;

    /**
     * 审核备注
     */
    private String checkRemark;

    /**
     * 损耗明细列表
     */
    @Valid
    @NotNull(message = "损耗明细列表不能为空")
    private List<ProductOrderLossDetailDTO> list;
}
