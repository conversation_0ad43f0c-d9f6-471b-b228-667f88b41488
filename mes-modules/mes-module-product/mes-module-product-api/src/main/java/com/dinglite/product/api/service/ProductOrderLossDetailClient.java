package com.dinglite.product.api.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.common.constant.feign.ProductApiConstant;
import com.dinglite.product.api.domain.dto.ProductOrderLossDetailDTO;
import com.dinglite.product.api.domain.query.ProductOrderLossDetailQuery;
import com.dinglite.product.api.service.fallback.ProductOrderLossDetailClientFallBackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;

/**
 * 生产工单损耗明细 Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@FeignClient(name = ProductApiConstant.FEIGN_NAME, contextId = "productOrderLossDetailClient", fallbackFactory = ProductOrderLossDetailClientFallBackFactory.class)
@RequestMapping("/product/product-order-loss-detail/api")
public interface ProductOrderLossDetailClient {

    /**
     * 获取损耗明细详情
     *
     * @param productOrderLossDetailId 损耗明细ID
     * @return 损耗明细DTO
     */
    @GetMapping("/getInfo")
    ProductOrderLossDetailDTO getInfo(@RequestParam Long productOrderLossDetailId);

    /**
     * 分页查询损耗明细
     *
     * @param productOrderLossDetailQuery 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    PageDTO<ProductOrderLossDetailDTO> page(@RequestBody ProductOrderLossDetailQuery productOrderLossDetailQuery);

    /**
     * 查询损耗明细列表
     *
     * @param productOrderLossDetailQuery 查询条件
     * @return 损耗明细列表
     */
    @PostMapping("/list")
    List<ProductOrderLossDetailDTO> list(@RequestBody ProductOrderLossDetailQuery productOrderLossDetailQuery);

    /**
     * 根据损耗单ID查询明细列表
     *
     * @param productOrderLossId 损耗单ID
     * @return 明细列表
     */
    @GetMapping("/by-loss-id")
    List<ProductOrderLossDetailDTO> listByLossId(@RequestParam Long productOrderLossId);

    /**
     * 根据物料ID查询损耗明细列表
     *
     * @param materialId 物料ID
     * @return 明细列表
     */
    @GetMapping("/by-material-id")
    List<ProductOrderLossDetailDTO> listByMaterialId(@RequestParam Long materialId);

    /**
     * 根据生产工单ID查询损耗明细列表
     *
     * @param productOrderId 生产工单ID
     * @return 明细列表
     */
    @GetMapping("/by-product-order-id")
    List<ProductOrderLossDetailDTO> listByProductOrderId(@RequestParam Long productOrderId);

    /**
     * 统计指定物料的损耗总数量
     *
     * @param materialId 物料ID
     * @return 损耗总数量
     */
    @GetMapping("/sum-loss-num")
    BigDecimal sumLossNumByMaterialId(@RequestParam Long materialId);

    /**
     * 查询指定时间范围内的损耗明细
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 明细列表
     */
    @GetMapping("/time-range")
    List<ProductOrderLossDetailDTO> listByTimeRange(@RequestParam String startTime,
                                                    @RequestParam String endTime);
}
