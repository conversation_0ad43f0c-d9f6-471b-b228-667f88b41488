package com.dinglite.product.api.domain.query;

import com.dinglite.common.global.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 生产工单损耗单查询对象
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ProductOrderLossQuery extends PageParam {

    /**
     * 生产工单损耗单ID
     */
    private Long productOrderLossId;

    /**
     * 生产工单损耗单流水号
     */
    private String productOrderLossCode;

    /**
     * 产线ID
     */
    private Long productLineId;

    /**
     * 产线名称
     */
    private String productLineName;

    /**
     * 生产工单ID
     */
    private Long productOrderId;

    /**
     * 生产工单编码
     */
    private String productOrderCode;

    /**
     * 生产工单名称
     */
    private String productOrderName;

    /**
     * 审核状态（0.待审核 1.审核通过 2.审核未通过）
     */
    private Integer status;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 审核人
     */
    private String checkBy;

    /**
     * 创建开始时间
     */
    private String createStartTime;

    /**
     * 创建结束时间
     */
    private String createEndTime;

    /**
     * 审核开始时间
     */
    private String checkStartTime;

    /**
     * 审核结束时间
     */
    private String checkEndTime;
}
