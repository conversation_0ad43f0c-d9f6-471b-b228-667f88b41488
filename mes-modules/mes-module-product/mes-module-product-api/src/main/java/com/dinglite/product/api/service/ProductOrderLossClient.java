package com.dinglite.product.api.service;

import com.dinglite.common.domain.PageDTO;
import com.dinglite.common.constant.feign.ProductApiConstant;
import com.dinglite.product.api.domain.dto.ProductOrderLossDTO;
import com.dinglite.product.api.domain.query.ProductOrderLossQuery;
import com.dinglite.product.api.service.fallback.ProductOrderLossClientFallBackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 生产工单损耗单 Feign客户端
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@FeignClient(name = ProductApiConstant.FEIGN_NAME, contextId = "productOrderLossClient", fallbackFactory = ProductOrderLossClientFallBackFactory.class)
@RequestMapping("/product/product-order-loss/api")
public interface ProductOrderLossClient {

    /**
     * 创建损耗单
     *
     * @param productOrderLossDTO 损耗单DTO
     * @return 损耗单DTO
     */
    @PostMapping
    ProductOrderLossDTO create(@RequestBody @Valid ProductOrderLossDTO productOrderLossDTO);

    /**
     * 获取损耗单详情
     *
     * @param productOrderLossId 损耗单ID
     * @return 损耗单DTO
     */
    @GetMapping("/getInfo")
    ProductOrderLossDTO getInfo(@RequestParam Long productOrderLossId);

    /**
     * 更新损耗单
     *
     * @param productOrderLossDTO 损耗单DTO
     * @return 是否成功
     */
    @PutMapping
    Boolean update(@RequestBody @Valid ProductOrderLossDTO productOrderLossDTO);

    /**
     * 删除损耗单
     *
     * @param productOrderLossIds 损耗单ID列表
     * @return 是否成功
     */
    @DeleteMapping
    Boolean delete(@RequestBody List<Long> productOrderLossIds);

    /**
     * 分页查询损耗单
     *
     * @param productOrderLossQuery 查询条件
     * @return 分页结果
     */
    @PostMapping("/page")
    PageDTO<ProductOrderLossDTO> page(@RequestBody ProductOrderLossQuery productOrderLossQuery);

    /**
     * 查询损耗单列表
     *
     * @param productOrderLossQuery 查询条件
     * @return 损耗单列表
     */
    @PostMapping("/list")
    List<ProductOrderLossDTO> list(@RequestBody ProductOrderLossQuery productOrderLossQuery);

    /**
     * 审核损耗单
     *
     * @param productOrderLossId 损耗单ID
     * @param status             审核状态
     * @param checkRemark        审核备注
     * @return 是否成功
     */
    @PostMapping("/approve")
    Boolean approve(@RequestParam Long productOrderLossId,
                   @RequestParam Integer status,
                   @RequestParam(required = false) String checkRemark);

    /**
     * 批量审核损耗单
     *
     * @param productOrderLossIds 损耗单ID列表
     * @param status              审核状态
     * @param checkRemark         审核备注
     * @return 是否成功
     */
    @PostMapping("/batch-approve")
    Boolean batchApprove(@RequestParam List<Long> productOrderLossIds,
                        @RequestParam Integer status,
                        @RequestParam(required = false) String checkRemark);

    /**
     * 根据生产工单ID查询损耗单列表
     *
     * @param productOrderId 生产工单ID
     * @return 损耗单列表
     */
    @GetMapping("/by-product-order")
    List<ProductOrderLossDTO> listByProductOrderId(@RequestParam Long productOrderId);

    /**
     * 根据产线ID查询损耗单列表
     *
     * @param productLineId 产线ID
     * @return 损耗单列表
     */
    @GetMapping("/by-product-line")
    List<ProductOrderLossDTO> listByProductLineId(@RequestParam Long productLineId);

    /**
     * 根据审核状态查询损耗单列表
     *
     * @param status 审核状态
     * @return 损耗单列表
     */
    @GetMapping("/by-status")
    List<ProductOrderLossDTO> listByStatus(@RequestParam Integer status);
}
