package com.dinglite.product.api.domain.query;

import com.dinglite.common.global.PageParam;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 生产工单损耗明细查询对象
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Data
@Accessors(chain = true)
@EqualsAndHashCode(callSuper = true)
public class ProductOrderLossDetailQuery extends PageParam {

    /**
     * 生产工单损耗明细ID
     */
    private Long productOrderLossDetailId;

    /**
     * 生产工单损耗单ID
     */
    private Long productOrderLossId;

    /**
     * 物料ID
     */
    private Long materialId;

    /**
     * 物料编码
     */
    private String materialCode;

    /**
     * 物料名称
     */
    private String materialName;

    /**
     * 创建人
     */
    private String createBy;
}
