package com.dinglite.product.api.service.fallback;

import com.dinglite.feign.config.hystrix.BasicFallbackFactory;
import com.dinglite.product.api.service.ProductOrderLossClient;
import org.springframework.stereotype.Component;

/**
 * 生产工单损耗单客户端降级处理
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
@Component
public class ProductOrderLossClientFallBackFactory extends BasicFallbackFactory<ProductOrderLossClient> {
    public ProductOrderLossClientFallBackFactory() {
        super(ProductOrderLossClient.class);
    }
}
