package com.dinglite.product.api.domain.constant;

/**
 * 生产工单损耗单常量类
 *
 * <AUTHOR>
 * @since 2025-07-29
 */
public class ProductOrderLossConstant {

    /**
     * 审核状态常量
     *
     * 业务逻辑说明：
     * - 待审核(0)：可以修改、删除
     * - 审核通过(1)：不可修改、不可删除（已确认的数据不应变更）
     * - 审核未通过(2)：可以修改、删除（允许用户根据审核意见调整后重新提交）
     */
    public static class Status {
        /** 待审核 - 可以修改和删除 */
        public static final Integer PENDING = 0;
        /** 审核通过 - 不可修改和删除 */
        public static final Integer APPROVED = 1;
        /** 审核未通过 - 可以修改和删除 */
        public static final Integer REJECTED = 2;
    }

    /**
     * 审核状态描述
     */
    public static class StatusDesc {
        /** 待审核 */
        public static final String PENDING = "待审核";
        /** 审核通过 */
        public static final String APPROVED = "审核通过";
        /** 审核未通过 */
        public static final String REJECTED = "审核未通过";
    }

    /**
     * 逻辑删除标识
     */
    public static class Flag {
        /** 未删除 */
        public static final String NOT_DELETED = "N";
        /** 已删除 */
        public static final String DELETED = "Y";
    }

    /**
     * 编码规则相关常量
     */
    public static class CodeRule {
        /** 损耗单表名 */
        public static final String TABLE_NAME = "product_order_loss";
        /** 损耗单编码字段名 */
        public static final String FIELD_NAME = "product_order_loss_code";
        /** 损耗单编码前缀 */
        public static final String CODE_PREFIX = "LOSS";
    }

    /**
     * 操作类型常量
     */
    public static class Operation {
        /** 创建 */
        public static final String CREATE = "CREATE";
        /** 更新 */
        public static final String UPDATE = "UPDATE";
        /** 审核 */
        public static final String APPROVE = "APPROVE";
        /** 驳回 */
        public static final String REJECT = "REJECT";
        /** 删除 */
        public static final String DELETE = "DELETE";
    }

    /**
     * 操作描述
     */
    public static class OperationDesc {
        /** 创建 */
        public static final String CREATE = "创建损耗单";
        /** 更新 */
        public static final String UPDATE = "更新损耗单";
        /** 审核通过 */
        public static final String APPROVE = "审核通过";
        /** 审核驳回 */
        public static final String REJECT = "审核驳回";
        /** 删除 */
        public static final String DELETE = "删除损耗单";
    }

    /**
     * 错误消息常量
     */
    public static class ErrorMessage {
        /** 损耗单不存在 */
        public static final String LOSS_NOT_FOUND = "损耗单不存在";
        /** 损耗单已审核通过 */
        public static final String LOSS_ALREADY_APPROVED = "损耗单已审核通过，无法修改";
        /** 损耗单无法修改 */
        public static final String LOSS_CANNOT_MODIFY = "损耗单当前状态不允许修改，只有待审核和审核未通过的损耗单可以修改";
        /** 损耗单无法删除 */
        public static final String LOSS_CANNOT_DELETE = "损耗单当前状态不允许删除，只有待审核和审核未通过的损耗单可以删除";
        /** 损耗明细不能为空 */
        public static final String LOSS_DETAIL_EMPTY = "损耗明细不能为空";
        /** 损耗数量必须大于0 */
        public static final String LOSS_NUM_INVALID = "损耗数量必须大于0";
        /** 物料信息不存在 */
        public static final String MATERIAL_NOT_FOUND = "物料信息不存在";
        /** 生产工单不存在 */
        public static final String PRODUCT_ORDER_NOT_FOUND = "生产工单不存在";
        /** 产线信息不存在 */
        public static final String PRODUCT_LINE_NOT_FOUND = "产线信息不存在";
        /** 无权限操作 */
        public static final String NO_PERMISSION = "无权限操作此损耗单";
    }

    /**
     * 成功消息常量
     */
    public static class SuccessMessage {
        /** 创建成功 */
        public static final String CREATE_SUCCESS = "损耗单创建成功";
        /** 更新成功 */
        public static final String UPDATE_SUCCESS = "损耗单更新成功";
        /** 审核成功 */
        public static final String APPROVE_SUCCESS = "损耗单审核成功";
        /** 驳回成功 */
        public static final String REJECT_SUCCESS = "损耗单驳回成功";
        /** 删除成功 */
        public static final String DELETE_SUCCESS = "损耗单删除成功";
    }
}
